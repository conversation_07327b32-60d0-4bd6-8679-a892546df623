-- Export complete database schema for Listless application
-- Run this in your PRODUCTION Supabase SQL Editor to get the schema

-- 1. Export all table definitions
SELECT 
    schemaname,
    tablename,
    pg_get_tabledef(schemaname||'.'||tablename) as table_definition
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- 2. Export all functions
SELECT 
    n.nspname as schema_name,
    p.proname as function_name,
    pg_get_functiondef(p.oid) as function_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
ORDER BY p.proname;

-- 3. Export all triggers
SELECT 
    schemaname,
    tablename,
    triggername,
    pg_get_triggerdef(oid) as trigger_definition
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public'
AND NOT tgisinternal
ORDER BY tablename, triggername;

-- 4. Export RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd,
    roles,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 5. Export indexes
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
ORDER BY tablename, indexname;
