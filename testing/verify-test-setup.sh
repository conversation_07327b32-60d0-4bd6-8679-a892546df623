#!/bin/bash

# Test Environment Verification Script for Listless Playwright Testing
# This script verifies that the test environment is properly configured

set -e  # Exit on any error

echo "🔍 Verifying Listless Test Environment Setup..."

# Define paths
PROJECT_ROOT="/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1"
APP_DIR="$PROJECT_ROOT/Listless_V0_8-6"

# Navigate to application directory
cd "$APP_DIR"
echo "📁 Working directory: $(pwd)"

# Check if .env.test exists
echo "1️⃣ Checking test environment configuration..."
if [ ! -f ".env.test" ]; then
    echo "❌ Error: .env.test file not found"
    echo "   Please create .env.test with your test project credentials"
    exit 1
fi

echo "✅ Found .env.test file"

# Load test environment variables
source .env.test

# Verify critical environment variables
echo "2️⃣ Verifying environment variables..."

if [ -z "$NEXT_PUBLIC_SUPABASE_URL" ]; then
    echo "❌ Error: NEXT_PUBLIC_SUPABASE_URL not set in .env.test"
    exit 1
fi

if [ -z "$NEXT_PUBLIC_SUPABASE_ANON_KEY" ]; then
    echo "❌ Error: NEXT_PUBLIC_SUPABASE_ANON_KEY not set in .env.test"
    exit 1
fi

if [ -z "$SUPABASE_SERVICE_ROLE_KEY" ]; then
    echo "❌ Error: SUPABASE_SERVICE_ROLE_KEY not set in .env.test"
    exit 1
fi

echo "✅ Required environment variables are set"

# Check for production database URL (CRITICAL SAFETY CHECK)
echo "3️⃣ Performing critical safety checks..."

if [[ "$NEXT_PUBLIC_SUPABASE_URL" == *"kdaiyodzdnphkiufagdu"* ]]; then
    echo "🚨 CRITICAL ERROR: .env.test is configured with PRODUCTION database!"
    echo "   Current URL: $NEXT_PUBLIC_SUPABASE_URL"
    echo "   This would run tests against your production data!"
    echo ""
    echo "IMMEDIATE ACTION REQUIRED:"
    echo "1. Update .env.test with your TEST project URL"
    echo "2. Never use production credentials in test environment"
    echo ""
    exit 1
fi

if [[ "$NEXT_PUBLIC_SUPABASE_URL" == *"YOUR_TEST_PROJECT_REF"* ]]; then
    echo "❌ Error: .env.test contains placeholder values"
    echo "   Please update with your actual test project credentials"
    echo "   Get them from: https://supabase.com/dashboard/project/YOUR_TEST_PROJECT/settings/api"
    exit 1
fi

echo "✅ Safety check passed - not using production database"

# Verify test environment flag
if [ "$TEST_ENVIRONMENT" != "true" ]; then
    echo "❌ Error: TEST_ENVIRONMENT not set to 'true' in .env.test"
    exit 1
fi

echo "✅ Test environment flag verified"

# Check if test files exist
echo "4️⃣ Checking test files..."

REQUIRED_TEST_FILES=(
    "tests/auth.test.ts"
    "tests/task-management.test.ts"
    "tests/project-area-management.test.ts"
    "tests/test-utils.ts"
    "tests/test-environment-setup.ts"
    "tests/test-environment-teardown.ts"
    "playwright.config.ts"
)

for file in "${REQUIRED_TEST_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ Error: Required test file not found: $file"
        exit 1
    fi
done

echo "✅ All required test files are present"

# Check Playwright installation
echo "5️⃣ Verifying Playwright installation..."

if ! npm list @playwright/test > /dev/null 2>&1; then
    echo "❌ Error: @playwright/test not installed"
    echo "   Run: npm install -D @playwright/test"
    exit 1
fi

echo "✅ Playwright is installed"

# Test database connectivity
echo "6️⃣ Testing database connectivity..."

HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" \
    -H "apikey: $NEXT_PUBLIC_SUPABASE_ANON_KEY" \
    -H "Authorization: Bearer $NEXT_PUBLIC_SUPABASE_ANON_KEY" \
    "$NEXT_PUBLIC_SUPABASE_URL/rest/v1/")

if [ "$HTTP_CODE" -eq 200 ]; then
    echo "✅ Test database connection successful"
else
    echo "❌ Error: Cannot connect to test database (HTTP $HTTP_CODE)"
    echo "   Please verify:"
    echo "   1. Test Supabase project is running"
    echo "   2. API keys in .env.test are correct"
    echo "   3. Network connectivity"
    exit 1
fi

# Display environment summary
echo ""
echo "📊 Test Environment Summary:"
echo "   Supabase URL: $NEXT_PUBLIC_SUPABASE_URL"
echo "   Node Environment: $NODE_ENV"
echo "   Test Environment: $TEST_ENVIRONMENT"
echo "   Base URL: $NEXT_PUBLIC_SITE_URL"
echo ""

echo "🎉 Test environment verification completed successfully!"
echo ""
echo "✅ Your test setup is ready:"
echo "   • Test environment properly configured"
echo "   • Production database completely isolated"
echo "   • All test files in place"
echo "   • Database connectivity verified"
echo ""
echo "🚀 Ready to run tests safely!"
echo ""
echo "Next steps:"
echo "   1. Ensure your test Supabase project has the database schema"
echo "   2. Start the application: npm run dev"
echo "   3. Run tests: npm run test:ui"
echo ""
echo "🛡️ Your production data is completely safe!"
