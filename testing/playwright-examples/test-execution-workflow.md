# Automated Test Execution Workflow for Listless

## 1. Pre-Test Setup

### Start Your Application
```bash
# Navigate to your Listless application directory
cd /Users/<USER>/_DEV\ PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6

# Start the development server
npm run dev

# Verify application is running at http://localhost:3000
```

### Prepare Test Environment
```bash
# Install Playwright if not already installed
npm install -D @playwright/test

# Install browsers
npx playwright install

# Create test directory structure
mkdir -p tests/{auth,tasks,projects,utils}
mkdir -p screenshots test-results playwright-report
```

## 2. Test Execution Commands

### Basic Test Execution
```bash
# Run all tests
npx playwright test

# Run specific test suites
npx playwright test tests/auth/
npx playwright test tests/tasks/
npx playwright test tests/projects/

# Run with specific configuration
npx playwright test --config=playwright.config.ts
```

### Interactive Testing
```bash
# Run tests in UI mode (recommended for development)
npx playwright test --ui

# Run tests in headed mode (visible browser)
npx playwright test --headed

# Debug specific test
npx playwright test auth.test.ts --debug
```

### Cross-Browser Testing
```bash
# Run on all configured browsers
npx playwright test --project=chromium --project=firefox --project=webkit

# Run on mobile devices
npx playwright test --project="Mobile Chrome" --project="Mobile Safari"
```

## 3. Test Data Management

### Setup Test Data
```typescript
// tests/setup/test-data.ts
export const testUsers = {
  admin: {
    email: '<EMAIL>',
    password: 'AdminPassword123!',
    name: 'Admin User'
  },
  regular: {
    email: '<EMAIL>', 
    password: 'UserPassword123!',
    name: 'Regular User'
  }
};

export const testTasks = [
  'Complete project documentation',
  'Review code changes',
  'Update user interface',
  'Test new features',
  'Deploy to staging'
];

export const testProjects = [
  { name: 'Website Redesign', area: 'Work' },
  { name: 'Mobile App', area: 'Development' },
  { name: 'Marketing Campaign', area: 'Marketing' }
];
```

### Database Cleanup
```typescript
// tests/setup/cleanup.ts
import { test as setup } from '@playwright/test';

setup('cleanup database', async ({ page }) => {
  // Login as admin
  await page.goto('/auth/login');
  await page.getByLabel('Email').fill('<EMAIL>');
  await page.getByLabel('Password').fill('AdminPassword123!');
  await page.getByRole('button', { name: 'Sign in' }).click();
  
  // Clear all test data
  await page.goto('/admin/cleanup'); // If you have an admin cleanup endpoint
  await page.getByRole('button', { name: 'Clear Test Data' }).click();
  
  // Logout
  await page.getByRole('button', { name: 'Sign out' }).click();
});
```

## 4. Comprehensive Test Reports

### HTML Report Generation
```bash
# Generate detailed HTML report
npx playwright test --reporter=html

# Open report in browser
npx playwright show-report
```

### Custom Report Configuration
```typescript
// playwright.config.ts
export default defineConfig({
  reporter: [
    ['html', { 
      outputFolder: 'playwright-report',
      open: 'never' // Don't auto-open in CI
    }],
    ['json', { 
      outputFile: 'test-results/results.json' 
    }],
    ['junit', { 
      outputFile: 'test-results/junit.xml' 
    }],
    ['allure-playwright'] // If using Allure
  ],
});
```

## 5. Screenshot and Video Capture

### Automatic Capture on Failure
```typescript
// Configured in playwright.config.ts
use: {
  screenshot: 'only-on-failure',
  video: 'retain-on-failure',
  trace: 'on-first-retry'
}
```

### Manual Screenshot Capture
```typescript
// In your tests
test('task creation with screenshot', async ({ page }) => {
  await page.goto('/dashboard');
  
  // Take screenshot before action
  await page.screenshot({ 
    path: 'screenshots/before-task-creation.png',
    fullPage: true 
  });
  
  await page.getByRole('button', { name: 'Add Task' }).click();
  await page.getByPlaceholder('Add task').fill('Test task');
  await page.keyboard.press('Enter');
  
  // Take screenshot after action
  await page.screenshot({ 
    path: 'screenshots/after-task-creation.png',
    fullPage: true 
  });
  
  // Attach to test report
  await test.info().attach('task-creation-flow', {
    body: await page.screenshot(),
    contentType: 'image/png'
  });
});
```

## 6. Continuous Integration Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/playwright.yml
name: Playwright Tests
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - uses: actions/setup-node@v3
      with:
        node-version: 18
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
      
    - name: Start Listless Application
      run: |
        npm run build
        npm run start &
        npx wait-on http://localhost:3000
        
    - name: Run Playwright tests
      run: npx playwright test
      
    - uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30
```

## 7. Test Maintenance and Reliability

### Page Object Model
```typescript
// tests/pages/dashboard.page.ts
export class DashboardPage {
  constructor(private page: Page) {}
  
  async goto() {
    await this.page.goto('/dashboard');
    await this.page.waitForLoadState('networkidle');
  }
  
  async createTask(content: string) {
    await this.page.getByRole('button', { name: 'Add Task' }).click();
    await this.page.getByPlaceholder('Add task').fill(content);
    await this.page.keyboard.press('Enter');
    
    // Wait for task to appear
    await this.page.getByText(content).waitFor();
  }
  
  async getTaskCount() {
    return await this.page.locator('[data-testid="task-item"]').count();
  }
}
```

### Retry Logic and Stability
```typescript
// tests/utils/retry-helpers.ts
export async function retryAction(
  action: () => Promise<void>,
  maxRetries: number = 3,
  delay: number = 1000
) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await action();
      return;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

// Usage in tests
test('flaky network operation', async ({ page }) => {
  await retryAction(async () => {
    await page.goto('/dashboard');
    await expect(page.getByRole('heading', { name: 'Dashboard' })).toBeVisible();
  });
});
```

## 8. Performance and Load Testing

### Basic Performance Metrics
```typescript
test('page load performance', async ({ page }) => {
  const startTime = Date.now();
  
  await page.goto('/dashboard');
  await page.waitForLoadState('networkidle');
  
  const loadTime = Date.now() - startTime;
  expect(loadTime).toBeLessThan(3000); // 3 seconds max
  
  // Measure specific metrics
  const metrics = await page.evaluate(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    return {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0
    };
  });
  
  console.log('Performance metrics:', metrics);
});
```

## 9. Accessibility Testing Integration

### Automated A11y Checks
```typescript
import { injectAxe, checkA11y } from 'axe-playwright';

test('accessibility compliance', async ({ page }) => {
  await page.goto('/dashboard');
  await injectAxe(page);
  
  // Check entire page
  await checkA11y(page);
  
  // Check specific component
  await checkA11y(page, '[data-testid="task-list"]');
  
  // Check with specific rules
  await checkA11y(page, null, {
    rules: {
      'color-contrast': { enabled: true },
      'keyboard-navigation': { enabled: true }
    }
  });
});
```

## 10. Test Result Analysis and Reporting

### Custom Test Reporter
```typescript
// tests/reporters/custom-reporter.ts
class CustomReporter {
  onTestEnd(test: TestCase, result: TestResult) {
    if (result.status === 'failed') {
      console.log(`❌ ${test.title} failed:`);
      console.log(`   Error: ${result.error?.message}`);
      console.log(`   Duration: ${result.duration}ms`);
    } else if (result.status === 'passed') {
      console.log(`✅ ${test.title} passed (${result.duration}ms)`);
    }
  }
  
  onEnd(result: FullResult) {
    console.log(`\n📊 Test Summary:`);
    console.log(`   Total: ${result.allTests.length}`);
    console.log(`   Passed: ${result.allTests.filter(t => t.outcome() === 'expected').length}`);
    console.log(`   Failed: ${result.allTests.filter(t => t.outcome() === 'unexpected').length}`);
    console.log(`   Duration: ${result.duration}ms`);
  }
}
```

## 11. Integration with Development Workflow

### Pre-commit Hooks
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "npx playwright test --grep='smoke'",
      "git add"
    ]
  }
}
```

### Test-Driven Development
```typescript
// Write failing test first
test('new feature: task templates', async ({ page }) => {
  await page.goto('/dashboard');
  
  // This will fail until feature is implemented
  await page.getByRole('button', { name: 'Templates' }).click();
  await page.getByText('Meeting Template').click();
  
  await expect(page.getByText('Prepare agenda')).toBeVisible();
  await expect(page.getByText('Send invites')).toBeVisible();
});
```

This workflow ensures comprehensive testing coverage while maintaining reliability and providing actionable feedback for your development process.
