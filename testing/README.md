# Testing Infrastructure

This directory contains all testing-related files, scripts, and documentation for the Listless application.

## 📁 Directory Contents

### 🎭 Playwright Test Examples (`playwright-examples/`)
- **Purpose**: Example test files and configurations for Playwright E2E testing
- **Contents**: 
  - Test files (`.test.ts`)
  - Configuration files (`playwright.config.ts`, `playwright-test.config.ts`)
  - Test utilities and setup files
  - User strategy implementations

### 🔧 Setup Scripts
- **`setup-playwright-tests.sh`**: Automated setup script for Playwright testing infrastructure
- **`setup-test-database.sh`**: Script to configure test database schema
- **`sync-test-schema.sh`**: Synchronizes database schema between environments
- **`verify-test-setup.sh`**: Validates that test environment is properly configured

### 📊 Database & Schema
- **`export-schema-query.sql`**: SQL queries for exporting database schema

### 📚 Documentation
- **`COMPLETE_TEST_SETUP_GUIDE.md`**: Comprehensive guide for setting up the complete testing environment
- **`CRITICAL_TEST_ISOLATION_SETUP.md`**: Critical setup instructions for test isolation
- **`MANUAL_TESTING_CHECKLIST.md`**: Manual testing procedures and checklists
- **`PLAYWRIGHT_SETUP_GUIDE.md`**: Specific guide for Playwright setup
- **`PLAYWRIGHT_USER_ACCOUNT_STRATEGY.md`**: Strategy for managing test user accounts
- **`PLAYWRIGHT_USER_NAMING_AND_RETENTION_STRATEGY.md`**: User naming conventions and retention policies
- **`PLAYWRIGHT_USER_STRATEGY_ANALYSIS.md`**: Analysis of different user management strategies

## 🚀 Quick Start

1. **Setup Testing Environment**:
   ```bash
   ./setup-playwright-tests.sh
   ```

2. **Configure Test Database**:
   ```bash
   ./setup-test-database.sh
   ```

3. **Verify Setup**:
   ```bash
   ./verify-test-setup.sh
   ```

## 📝 Notes

- All scripts are designed to work from the parent directory context
- Test configurations are set up to work with the Listless application in `../Listless_V0_8-6/`
- Ensure proper environment variables are configured before running tests

## 🔗 Related

- Main application: `../Listless_V0_8-6/`
- Configuration files: `../config/`
- Documentation: `../docs/`
