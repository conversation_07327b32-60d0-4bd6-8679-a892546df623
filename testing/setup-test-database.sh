#!/bin/bash

# Setup Test Database Schema for Listless Playwright Testing
# This script applies your existing migrations to a new test Supabase project

set -e  # Exit on any error

echo "🗄️ Setting up test database schema..."

# Configuration - UPDATE THESE WITH YOUR TEST PROJECT DETAILS
TEST_PROJECT_URL="https://YOUR_TEST_PROJECT_REF.supabase.co"
TEST_SERVICE_KEY="YOUR_TEST_SERVICE_ROLE_KEY"

# Validate configuration
if [[ "$TEST_PROJECT_URL" == *"YOUR_TEST_PROJECT_REF"* ]]; then
    echo "❌ Error: Please update TEST_PROJECT_URL with your actual test project URL"
    echo "   Get this from: https://supabase.com/dashboard/project/YOUR_TEST_PROJECT/settings/api"
    exit 1
fi

if [[ "$TEST_SERVICE_KEY" == "YOUR_TEST_SERVICE_ROLE_KEY" ]]; then
    echo "❌ Error: Please update TEST_SERVICE_KEY with your actual service role key"
    echo "   Get this from: https://supabase.com/dashboard/project/YOUR_TEST_PROJECT/settings/api"
    exit 1
fi

echo "✅ Configuration validated"
echo "🎯 Target: $TEST_PROJECT_URL"

# Function to execute SQL file against test database
execute_sql_file() {
    local file_path="$1"
    local description="$2"
    
    echo "📄 Executing: $description"
    echo "   File: $file_path"
    
    if [ ! -f "$file_path" ]; then
        echo "❌ Error: File not found: $file_path"
        exit 1
    fi
    
    # Execute SQL file using curl to Supabase REST API
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        "$TEST_PROJECT_URL/rest/v1/rpc/exec_sql" \
        -H "Authorization: Bearer $TEST_SERVICE_KEY" \
        -H "apikey: $TEST_SERVICE_KEY" \
        -H "Content-Type: application/json" \
        -d "{\"sql\": $(cat "$file_path" | jq -Rs .)}")
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ]; then
        echo "   ✅ Success"
    else
        echo "   ❌ Failed (HTTP $http_code)"
        echo "   Response: $response_body"
        exit 1
    fi
}

# Alternative function using psql if available
execute_sql_file_psql() {
    local file_path="$1"
    local description="$2"
    
    echo "📄 Executing: $description"
    echo "   File: $file_path"
    
    # Extract connection details from URL
    PROJECT_REF=$(echo "$TEST_PROJECT_URL" | sed 's/https:\/\/\([^.]*\).*/\1/')
    
    # Use psql to execute (requires psql and proper connection string)
    PGPASSWORD="$TEST_DB_PASSWORD" psql \
        "************************************************************************/postgres" \
        -f "$file_path"
    
    if [ $? -eq 0 ]; then
        echo "   ✅ Success"
    else
        echo "   ❌ Failed"
        exit 1
    fi
}

# Navigate to project directory
cd "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6"

echo "📁 Working directory: $(pwd)"

# Check if migration files exist
MIGRATIONS_DIR="supabase/migrations"
if [ ! -d "$MIGRATIONS_DIR" ]; then
    echo "❌ Error: Migrations directory not found: $MIGRATIONS_DIR"
    exit 1
fi

echo "✅ Found migrations directory: $MIGRATIONS_DIR"

# List migration files
echo "📋 Available migration files:"
ls -la "$MIGRATIONS_DIR"/*.sql

echo ""
echo "🚀 Starting database schema setup..."

# Execute migration files in order
echo "1️⃣ Creating initial schema..."
execute_sql_file "$MIGRATIONS_DIR/001_initial_schema.sql" "Initial Schema (Tables, Indexes, Extensions)"

echo "2️⃣ Setting up RLS policies..."
execute_sql_file "$MIGRATIONS_DIR/002_rls_policies.sql" "Row Level Security Policies"

echo "3️⃣ Creating functions..."
execute_sql_file "$MIGRATIONS_DIR/003_functions.sql" "Database Functions"

echo "4️⃣ Setting up triggers..."
execute_sql_file "$MIGRATIONS_DIR/004_triggers.sql" "Database Triggers"

echo "5️⃣ Applying fixes..."
execute_sql_file "$MIGRATIONS_DIR/005_fix_user_profile_creation.sql" "User Profile Creation Fix"

echo ""
echo "🎉 Test database schema setup completed successfully!"
echo ""
echo "✅ Your test database now has:"
echo "   • All tables with proper structure"
echo "   • Vector extension for AI embeddings"
echo "   • Row Level Security policies"
echo "   • Database functions and triggers"
echo "   • Performance indexes"
echo ""
echo "🔒 Next steps:"
echo "   1. Create .env.test file with test project credentials"
echo "   2. Configure Playwright to use test environment"
echo "   3. Run verification tests"
echo ""
echo "🛡️ Your production database remains completely untouched!"
