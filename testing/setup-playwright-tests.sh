#!/bin/bash

# Playwright Test Setup Script for Listless Application
# This script sets up automated testing for TaskMaster AI Tasks 1-4

set -e  # Exit on any error

echo "🎭 Setting up Playwright tests for Listless application..."

# Define paths
PROJECT_ROOT="/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1"
APP_DIR="$PROJECT_ROOT/Listless_V0_8-6"
EXAMPLES_DIR="$PROJECT_ROOT/testing/playwright-examples"
TESTS_DIR="$APP_DIR/tests"

# Check if we're in the right location
if [ ! -d "$APP_DIR" ]; then
    echo "❌ Error: Listless application directory not found at $APP_DIR"
    echo "Please ensure you're running this from the correct location."
    exit 1
fi

if [ ! -d "$EXAMPLES_DIR" ]; then
    echo "❌ Error: Playwright examples directory not found at $EXAMPLES_DIR"
    echo "Please ensure the test files have been created."
    exit 1
fi

echo "✅ Found application directory: $APP_DIR"
echo "✅ Found examples directory: $EXAMPLES_DIR"

# Navigate to application directory
cd "$APP_DIR"
echo "📁 Changed to application directory: $(pwd)"

# Check if Node.js is available
if ! command -v npm &> /dev/null; then
    echo "❌ Error: npm is not installed or not in PATH"
    echo "Please install Node.js and npm first."
    exit 1
fi

echo "✅ Node.js and npm are available"

# Install Playwright dependencies
echo "📦 Installing Playwright dependencies..."
npm install -D @playwright/test

# Install browser binaries
echo "🌐 Installing browser binaries..."
npx playwright install

# Create tests directory
echo "📁 Creating tests directory..."
mkdir -p "$TESTS_DIR"

# Copy test files
echo "📋 Copying test files from examples..."
cp "$EXAMPLES_DIR"/*.ts "$TESTS_DIR/"
cp "$EXAMPLES_DIR"/*.md "$TESTS_DIR/"

# List copied files
echo "✅ Copied test files:"
ls -la "$TESTS_DIR"

# Update package.json with test scripts
echo "📝 Adding test scripts to package.json..."

# Create a temporary file with the updated package.json
node -e "
const fs = require('fs');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

// Add test scripts if they don't exist
if (!packageJson.scripts) packageJson.scripts = {};

const testScripts = {
  'test': 'playwright test',
  'test:ui': 'playwright test --ui',
  'test:headed': 'playwright test --headed',
  'test:debug': 'playwright test --debug',
  'test:report': 'playwright show-report',
  'test:auth': 'playwright test tests/auth.test.ts',
  'test:tasks': 'playwright test tests/task-management.test.ts',
  'test:projects': 'playwright test tests/project-area-management.test.ts'
};

Object.assign(packageJson.scripts, testScripts);

fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
console.log('✅ Updated package.json with test scripts');
"

# Create .env.test file if it doesn't exist
if [ ! -f ".env.test" ]; then
    echo "🔧 Creating test environment file..."
    cat > .env.test << EOF
# Test environment variables for Playwright
NEXT_PUBLIC_SUPABASE_URL=\${NEXT_PUBLIC_SUPABASE_URL}
NEXT_PUBLIC_SUPABASE_ANON_KEY=\${NEXT_PUBLIC_SUPABASE_ANON_KEY}
SUPABASE_SERVICE_ROLE_KEY=\${SUPABASE_SERVICE_ROLE_KEY}
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Test-specific settings
NODE_ENV=test
PLAYWRIGHT_HEADLESS=false
EOF
    echo "✅ Created .env.test file"
fi

# Create a simple test runner script
cat > run-tests.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting Listless application and running tests..."

# Start the application in background
echo "📱 Starting application..."
npm run dev &
APP_PID=$!

# Wait for application to start
echo "⏳ Waiting for application to start..."
sleep 15

# Check if application is running
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Application is running at http://localhost:3000"
    
    # Run tests
    echo "🧪 Running Playwright tests..."
    npm run test:ui
    
    echo "📊 Test execution completed!"
    echo "📋 View results with: npm run test:report"
else
    echo "❌ Application failed to start"
    kill $APP_PID 2>/dev/null
    exit 1
fi

# Clean up
echo "🧹 Cleaning up..."
kill $APP_PID 2>/dev/null
echo "✅ Setup and test execution completed!"
EOF

chmod +x run-tests.sh

echo ""
echo "🎉 Playwright test setup completed successfully!"
echo ""
echo "📋 What was set up:"
echo "   ✅ Playwright dependencies installed"
echo "   ✅ Browser binaries installed"
echo "   ✅ Test files copied to tests/ directory"
echo "   ✅ Package.json updated with test scripts"
echo "   ✅ Environment configuration created"
echo "   ✅ Test runner script created"
echo ""
echo "🚀 To run tests:"
echo "   Option 1: ./run-tests.sh (automated)"
echo "   Option 2: npm run dev (in one terminal) && npm run test:ui (in another)"
echo "   Option 3: npm run test (headless mode)"
echo ""
echo "📊 Test Coverage:"
echo "   ✅ Authentication flows (TaskMaster Task 2)"
echo "   ✅ Task management CRUD (TaskMaster Task 3)"
echo "   ✅ Project/Area management (TaskMaster Task 4)"
echo "   ✅ Data persistence (TaskMaster Task 1)"
echo ""
echo "📁 Test files location: $TESTS_DIR"
echo "📖 Setup guide: $PROJECT_ROOT/PLAYWRIGHT_SETUP_GUIDE.md"
echo ""
echo "🤖 Ready for execution by another Augment agent!"
