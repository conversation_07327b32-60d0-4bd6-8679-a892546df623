# Configuration Files

This directory contains configuration files and templates for the development environment.

## 📁 Directory Contents

### 🔐 Environment Configuration
- **`.env.example`**: Template file showing required environment variables for TaskMaster AI integration
  - Contains API key placeholders for various AI providers (Anthropic, OpenAI, Perplexity, etc.)
  - Copy to `.env` and fill in actual API keys for development

## 🚀 Setup Instructions

### Environment Variables Setup
1. **Copy the example file**:
   ```bash
   cp config/.env.example .env
   ```

2. **Fill in your API keys**:
   - `ANTHROPIC_API_KEY`: Required for TaskMaster AI functionality
   - `PERPLEXITY_API_KEY`: Optional, for research capabilities
   - `OPENAI_API_KEY`: Optional, for OpenAI models
   - `GOOGLE_API_KEY`: Optional, for Google Gemini models
   - Other keys as needed for specific AI providers

3. **Security Note**: 
   - Never commit actual API keys to version control
   - The `.env` file is already included in `.gitignore`

## 📝 Notes

- Configuration files in this directory are for the parent development environment
- The main Listless application has its own configuration in `../Listless_V0_8-6/`
- Environment variables here are primarily for TaskMaster AI and development tools

## 🔗 Related

- Main application config: `../Listless_V0_8-6/`
- Testing setup: `../testing/`
- Documentation: `../docs/`
