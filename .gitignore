# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Task files
tasks.json
tasks/

# Development tool configurations (contain API keys)
.cursor/
.roo/
.roomodes
.windsurfrules

# Build outputs
.next/
dist/
build/

# Backup files
*.backup

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# TaskMaster AI configuration
.taskmaster/