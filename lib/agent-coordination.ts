/**
 * Agent Coordination Utilities
 * 
 * This module provides utilities for coordinating work between multiple
 * Remote Agents working on the same codebase.
 */

export interface AgentWorkStatus {
  agentId: string
  branchName: string
  filesInProgress: string[]
  estimatedCompletion: Date
  priority: 'low' | 'medium' | 'high' | 'critical'
}

export interface FileConflictInfo {
  filePath: string
  conflictingAgents: string[]
  conflictType: 'simultaneous_edit' | 'dependency_change' | 'type_modification'
  resolutionStrategy: 'merge' | 'sequential' | 'manual_review'
}

/**
 * Simulates checking for potential file conflicts between agents
 * In a real implementation, this would integrate with your git/CI system
 */
export function detectPotentialConflicts(
  agentStatuses: AgentWorkStatus[]
): FileConflictInfo[] {
  const conflicts: FileConflictInfo[] = []
  const fileAgentMap = new Map<string, string[]>()

  // Build map of files to agents working on them
  agentStatuses.forEach(status => {
    status.filesInProgress.forEach(file => {
      if (!fileAgentMap.has(file)) {
        fileAgentMap.set(file, [])
      }
      fileAgentMap.get(file)!.push(status.agentId)
    })
  })

  // Identify conflicts
  fileAgentMap.forEach((agents, filePath) => {
    if (agents.length > 1) {
      conflicts.push({
        filePath,
        conflictingAgents: agents,
        conflictType: determineConflictType(filePath),
        resolutionStrategy: determineResolutionStrategy(filePath, agents.length)
      })
    }
  })

  return conflicts
}

/**
 * Determines the type of conflict based on file path patterns
 */
function determineConflictType(filePath: string): FileConflictInfo['conflictType'] {
  if (filePath.includes('types.ts') || filePath.includes('interfaces.ts')) {
    return 'type_modification'
  }
  if (filePath.includes('lib/') && filePath.includes('utils')) {
    return 'dependency_change'
  }
  return 'simultaneous_edit'
}

/**
 * Suggests resolution strategy based on file importance and agent count
 */
function determineResolutionStrategy(
  filePath: string, 
  agentCount: number
): FileConflictInfo['resolutionStrategy'] {
  const criticalFiles = [
    'lib/supabase/types.ts',
    'lib/auth/',
    'app/layout.tsx',
    'supabase/migrations/'
  ]

  const isCritical = criticalFiles.some(pattern => filePath.includes(pattern))
  
  if (isCritical || agentCount > 2) {
    return 'manual_review'
  }
  if (filePath.includes('docs/') || filePath.includes('README')) {
    return 'merge'
  }
  return 'sequential'
}

/**
 * Generates a coordination report for human review
 */
export function generateCoordinationReport(
  agentStatuses: AgentWorkStatus[],
  conflicts: FileConflictInfo[]
): string {
  const report = []
  
  report.push('# Agent Coordination Report')
  report.push(`Generated: ${new Date().toISOString()}`)
  report.push('')
  
  report.push('## Active Agents')
  agentStatuses.forEach(status => {
    report.push(`- **${status.agentId}** (${status.priority} priority)`)
    report.push(`  - Branch: ${status.branchName}`)
    report.push(`  - Files: ${status.filesInProgress.length}`)
    report.push(`  - ETA: ${status.estimatedCompletion.toLocaleDateString()}`)
  })
  
  report.push('')
  report.push('## Potential Conflicts')
  if (conflicts.length === 0) {
    report.push('✅ No conflicts detected')
  } else {
    conflicts.forEach(conflict => {
      report.push(`- **${conflict.filePath}**`)
      report.push(`  - Agents: ${conflict.conflictingAgents.join(', ')}`)
      report.push(`  - Type: ${conflict.conflictType}`)
      report.push(`  - Strategy: ${conflict.resolutionStrategy}`)
    })
  }
  
  return report.join('\n')
}

/**
 * Example usage for testing the coordination system
 */
export function createTestScenario(): {
  agents: AgentWorkStatus[]
  conflicts: FileConflictInfo[]
  report: string
} {
  const agents: AgentWorkStatus[] = [
    {
      agentId: 'agent-alpha',
      branchName: 'feature/task-reordering',
      filesInProgress: [
        'app/api/reorder/route.ts',
        'lib/api/reorder-utils.ts',
        'components/task/task-list.tsx'
      ],
      estimatedCompletion: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days
      priority: 'high'
    },
    {
      agentId: 'agent-beta',
      branchName: 'feature/project-management',
      filesInProgress: [
        'app/api/projects/route.ts',
        'lib/supabase/types.ts', // Potential conflict!
        'components/project/project-card.tsx'
      ],
      estimatedCompletion: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days
      priority: 'medium'
    },
    {
      agentId: 'agent-gamma',
      branchName: 'docs/api-documentation',
      filesInProgress: [
        'docs/API_DOCUMENTATION.md',
        'README.md',
        'lib/supabase/types.ts' // Another potential conflict!
      ],
      estimatedCompletion: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 day
      priority: 'low'
    }
  ]

  const conflicts = detectPotentialConflicts(agents)
  const report = generateCoordinationReport(agents, conflicts)

  return { agents, conflicts, report }
}
