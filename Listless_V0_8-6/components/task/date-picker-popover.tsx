"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { CalendarIcon } from "lucide-react"

interface DatePickerPopoverProps {
  onSelect: (date: Date | undefined) => void
  placeholder?: string
  initialDate?: Date
  className?: string
}

export function DatePickerPopover({
  onSelect,
  placeholder = "Select date",
  initialDate,
  className,
}: DatePickerPopoverProps) {
  const [date, setDate] = React.useState<Date | undefined>(initialDate)
  const [open, setOpen] = React.useState(false)

  const handleSelect = (selectedDate: Date | undefined) => {
    setDate(selectedDate)
    onSelect(selectedDate)
    setOpen(false)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground", className)}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "PPP") : placeholder}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleSelect}
          className="fixed-calendar"
          showOutsideDays
          fixedWeeks
        />
      </PopoverContent>
    </Popover>
  )
}
