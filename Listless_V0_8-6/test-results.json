
> my-v0-project@0.1.0 test
> playwright test --reporter=json

✅ Test environment verified: https://rxdwcvzxgzwuapdxxbnt.supabase.co
🚀 Starting Playwright test environment setup...
✅ Test environment validated: Test Supabase instance
   Database URL: https://rxdwcvzxgzwuapdxxbnt.supabase.co
✅ Database connectivity verified
🧹 Cleaning up existing test data...
🔍 Found 4 existing test users to clean up
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
Cleanup query failed: DELETE FROM tasks WHERE title LIKE '%test%' OR title LIKE '%Test%' OR title LIKE '%playwright%'
Cleanup query failed: DELETE FROM projects WHERE name LIKE '%test%' OR name LIKE '%Test%' OR name LIKE '%playwright%'
Cleanup query failed: DELETE FROM areas WHERE name LIKE '%test%' OR name LIKE '%Test%' OR name LIKE '%playwright%'
✅ Existing test data cleaned up
✅ Test environment verified clean - no test users found
✅ Test database schema verified
✅ Test environment setup completed successfully
   Environment: Test Supabase Project
   Database: https://rxdwcvzxgzwuapdxxbnt.supabase.co
🧹 Starting comprehensive test environment cleanup...
🔄 Cleanup attempt 1/3...
🔍 Found 12 test users to delete
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🗑️ Deleted test user: <EMAIL>
🧹 Cleaning up orphaned test data...
⚠️ Failed to cleanup Test Tasks: Internal Server Error
⚠️ Failed to cleanup Test Projects: Internal Server Error
⚠️ Failed to cleanup Test Areas: Internal Server Error
⚠️ Failed to cleanup Test Tags: Internal Server Error
🔄 Resetting test database state...
✅ Test database state reset completed
✅ Cleanup attempt 1 completed successfully
   Deleted 12 test users
🔍 Verifying cleanup completion...
✅ Verification passed: No test users remain
✅ Test Tasks verified clean
✅ Test Areas verified clean
📊 Cleanup Report: {
  "timestamp": "2025-06-17T23:10:28.920Z",
  "environment": "https://rxdwcvzxgzwuapdxxbnt.supabase.co",
  "cleanupCompleted": true,
  "notes": "Comprehensive test cleanup executed successfully"
}
📄 Cleanup report saved to: /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/cleanup-report.json
✅ Test environment cleanup completed successfully
{
  "config": {
    "configFile": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/playwright.config.ts",
    "rootDir": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests",
    "forbidOnly": false,
    "fullyParallel": true,
    "globalSetup": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-environment-setup.ts",
    "globalTeardown": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-environment-teardown.ts",
    "globalTimeout": 0,
    "grep": {},
    "grepInvert": null,
    "maxFailures": 0,
    "metadata": {
      "actualWorkers": 5
    },
    "preserveOutput": "always",
    "reporter": [
      [
        "json"
      ]
    ],
    "reportSlowTests": {
      "max": 5,
      "threshold": 300000
    },
    "quiet": false,
    "projects": [
      {
        "outputDir": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results",
        "repeatEach": 1,
        "retries": 0,
        "metadata": {
          "actualWorkers": 5
        },
        "id": "chromium",
        "name": "chromium",
        "testDir": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 60000
      }
    ],
    "shard": null,
    "updateSnapshots": "missing",
    "updateSourceMethod": "patch",
    "version": "1.53.0",
    "workers": 5,
    "webServer": {
      "command": "npm run dev",
      "url": "http://localhost:3000",
      "reuseExistingServer": true,
      "timeout": 120000,
      "env": {
        "TERM_PROGRAM": "vscode",
        "NODE": "/opt/homebrew/Cellar/node/23.6.1/bin/node",
        "INIT_CWD": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6",
        "SHELL": "/bin/zsh",
        "TERM": "xterm-256color",
        "TMPDIR": "/var/folders/dw/wft13hf93zsczjxh4myf5q3r0000gn/T/",
        "HOMEBREW_REPOSITORY": "/opt/homebrew",
        "npm_config_global_prefix": "/opt/homebrew",
        "TERM_PROGRAM_VERSION": "1.101.0",
        "MallocSpaceEfficient": "1",
        "MallocNanoZone": "1",
        "ORIGINAL_XDG_CURRENT_DESKTOP": "undefined",
        "COLOR": "0",
        "npm_config_noproxy": "",
        "npm_config_local_prefix": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6",
        "EXTENSION_KIT_EXTENSION_TYPE": "2",
        "USER": "joshsmith",
        "COMMAND_MODE": "unix2003",
        "npm_config_globalconfig": "/opt/homebrew/etc/npmrc",
        "SSH_AUTH_SOCK": "/private/tmp/com.apple.launchd.q4OGTtseig/Listeners",
        "__CF_USER_TEXT_ENCODING": "0x1F5:0x0:0x0",
        "npm_execpath": "/opt/homebrew/lib/node_modules/npm/bin/npm-cli.js",
        "PAGER": "cat",
        "PATH": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/node_modules/.bin:/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/node_modules/.bin:/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/node_modules/.bin:/Users/<USER>/_DEV PROJECTS/_VSCode/node_modules/.bin:/Users/<USER>/_DEV PROJECTS/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/opt/homebrew/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.rbenv/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand",
        "npm_package_json": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/package.json",
        "_": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/node_modules/.bin/playwright",
        "npm_config_userconfig": "/Users/<USER>/.npmrc",
        "npm_config_init_module": "/Users/<USER>/.npm-init.js",
        "__CFBundleIdentifier": "com.microsoft.VSCode",
        "npm_command": "run-script",
        "PWD": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6",
        "npm_lifecycle_event": "test",
        "EDITOR": "vi",
        "npm_package_name": "my-v0-project",
        "LANG": "en_US.UTF-8",
        "npm_config_npm_version": "10.9.2",
        "XPC_FLAGS": "0x0",
        "VSCODE_GIT_ASKPASS_EXTRA_ARGS": "",
        "npm_config_node_gyp": "/opt/homebrew/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js",
        "RBENV_SHELL": "zsh",
        "npm_package_version": "0.1.0",
        "XPC_SERVICE_NAME": "0",
        "HOME": "/Users/<USER>",
        "SHLVL": "2",
        "VSCODE_GIT_ASKPASS_MAIN": "/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js",
        "HOMEBREW_PREFIX": "/opt/homebrew",
        "npm_config_cache": "/Users/<USER>/.npm",
        "LOGNAME": "joshsmith",
        "LESS": "-FX",
        "npm_lifecycle_script": "playwright test",
        "VSCODE_GIT_IPC_HANDLE": "/var/folders/dw/wft13hf93zsczjxh4myf5q3r0000gn/T/vscode-git-fb6c6ae1c2.sock",
        "npm_config_user_agent": "npm/10.9.2 node/v23.6.1 darwin arm64 workspaces/false",
        "HOMEBREW_CELLAR": "/opt/homebrew/Cellar",
        "INFOPATH": "/opt/homebrew/share/info:",
        "GIT_ASKPASS": "/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh",
        "VSCODE_GIT_ASKPASS_NODE": "/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)",
        "GIT_PAGER": "cat",
        "npm_node_execpath": "/opt/homebrew/Cellar/node/23.6.1/bin/node",
        "npm_config_prefix": "/opt/homebrew",
        "COLORTERM": "truecolor",
        "NEXT_PUBLIC_SUPABASE_URL": "https://rxdwcvzxgzwuapdxxbnt.supabase.co",
        "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ4ZHdjdnp4Z3p3dWFwZHh4Ym50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwODcxMzcsImV4cCI6MjA2NTY2MzEzN30.93S6YrCt-5h-u04KrkekOANPBjKkB95rjc_OsiXWWNk",
        "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ4ZHdjdnp4Z3p3dWFwZHh4Ym50Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA4NzEzNywiZXhwIjoyMDY1NjYzMTM3fQ.BE2D-f0hz7cnlaFdyjVW46euXvAYbM1mRyWs8NMjHLg",
        "NEXT_PUBLIC_SITE_URL": "http://localhost:3000",
        "NODE_ENV": "test",
        "PLAYWRIGHT_HEADLESS": "false",
        "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
        "TEST_ENVIRONMENT": "true",
        "TEST_PROJECT_NAME": "listless-testing",
        "PLAYWRIGHT_BASE_URL": "http://localhost:3000",
        "PLAYWRIGHT_TIMEOUT": "30000",
        "PLAYWRIGHT_RETRIES": "2",
        "TEST_USER_PREFIX": "tm2-playwright-test",
        "TEST_USER_DOMAIN": "listless-testing.local",
        "TEST_USER_RETENTION_HOURS": "24"
      }
    }
  },
  "suites": [
    {
      "title": "auth-optimized.test.ts",
      "file": "auth-optimized.test.ts",
      "column": 0,
      "line": 0,
      "specs": [
        {
          "title": "optimized user setup",
          "ok": true,
          "tags": [],
          "tests": [
            {
              "timeout": 60000,
              "annotations": [],
              "expectedStatus": "passed",
              "projectId": "chromium",
              "projectName": "chromium",
              "results": [
                {
                  "workerIndex": 0,
                  "parallelIndex": 0,
                  "status": "passed",
                  "duration": 1,
                  "errors": [],
                  "stdout": [
                    {
                      "text": "🚀 Starting optimized Playwright test setup...\n"
                    },
                    {
                      "text": "🚨 Emergency cleanup: deleting all test users...\n"
                    },
                    {
                      "text": "✅ Emergency cleanup completed\n"
                    },
                    {
                      "text": "✅ Optimized test setup completed\n"
                    }
                  ],
                  "stderr": [],
                  "retry": 0,
                  "startTime": "2025-06-17T23:08:55.315Z",
                  "annotations": [],
                  "attachments": []
                }
              ],
              "status": "expected"
            }
          ],
          "id": "f3521e5c0afb637e8e11-c47d8f23c2859653034b",
          "file": "optimized-user-strategy.ts",
          "line": 241,
          "column": 6
        },
        {
          "title": "optimized user teardown",
          "ok": true,
          "tags": [],
          "tests": [
            {
              "timeout": 60000,
              "annotations": [],
              "expectedStatus": "passed",
              "projectId": "chromium",
              "projectName": "chromium",
              "results": [
                {
                  "workerIndex": 1,
                  "parallelIndex": 1,
                  "status": "passed",
                  "duration": 2,
                  "errors": [],
                  "stdout": [
                    {
                      "text": "🧹 Starting optimized test teardown...\n"
                    },
                    {
                      "text": "🚨 Emergency cleanup: deleting all test users...\n"
                    },
                    {
                      "text": "✅ Emergency cleanup completed\n"
                    },
                    {
                      "text": "📊 Final stats: { totalUsers: \u001b[33m0\u001b[39m, suiteCount: \u001b[33m0\u001b[39m, suites: [], createdUserIds: [] }\n"
                    },
                    {
                      "text": "✅ Optimized test teardown completed\n"
                    }
                  ],
                  "stderr": [],
                  "retry": 0,
                  "startTime": "2025-06-17T23:08:55.310Z",
                  "annotations": [],
                  "attachments": []
                }
              ],
              "status": "expected"
            }
          ],
          "id": "f3521e5c0afb637e8e11-2e78c42576214ee502d4",
          "file": "optimized-user-strategy.ts",
          "line": 266,
          "column": 6
        }
      ],
      "suites": [
        {
          "title": "Authentication Flow - TaskMaster Task 2",
          "file": "auth-optimized.test.ts",
          "line": 11,
          "column": 6,
          "specs": [
            {
              "title": "User Registration Flow",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 3,
                      "parallelIndex": 3,
                      "status": "failed",
                      "duration": 0,
                      "error": {
                        "message": "Error: Failed to create user for user-login: 422 Unprocessable Entity - {\"code\":422,\"error_code\":\"email_exists\",\"msg\":\"A user with this email address has already been registered\"}",
                        "stack": "Error: Failed to create user for user-login: 422 Unprocessable Entity - {\"code\":422,\"error_code\":\"email_exists\",\"msg\":\"A user with this email address has already been registered\"}\n    at Function.createTaskMasterUserBatch (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/enhanced-user-strategy.ts:95:15)\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:17:17",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/enhanced-user-strategy.ts",
                          "column": 15,
                          "line": 95
                        },
                        "snippet": "\u001b[90m   at \u001b[39menhanced-user-strategy.ts:95\n\n  93 |           error: errorText\n  94 |         });\n> 95 |         throw new Error(`Failed to create user for ${scenario}: ${response.status} ${response.statusText} - ${errorText}`);\n     |               ^\n  96 |       }\n  97 |\n  98 |       const result = await response.json();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/enhanced-user-strategy.ts",
                            "column": 15,
                            "line": 95
                          },
                          "message": "Error: Failed to create user for user-login: 422 Unprocessable Entity - {\"code\":422,\"error_code\":\"email_exists\",\"msg\":\"A user with this email address has already been registered\"}\n\n   at enhanced-user-strategy.ts:95\n\n  93 |           error: errorText\n  94 |         });\n> 95 |         throw new Error(`Failed to create user for ${scenario}: ${response.status} ${response.statusText} - ${errorText}`);\n     |               ^\n  96 |       }\n  97 |\n  98 |       const result = await response.json();\n    at Function.createTaskMasterUserBatch (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/enhanced-user-strategy.ts:95:15)\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:17:17"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🏭 Creating TaskMaster 2 user batch: Authentication System\n"
                        },
                        {
                          "text": "   Test scenarios: user-registration, user-login, password-reset, logout-functionality, session-persistence, form-validation\n"
                        },
                        {
                          "text": "   Retention policy: debug\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-registration | <EMAIL>\n"
                        },
                        {
                          "text": "🧹 Performing smart cleanup with retention policies...\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "🗑️ Deleted: <EMAIL>\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "🗑️ Deleted: <EMAIL>\n"
                        },
                        {
                          "text": "🗑️ Deleted: <EMAIL>\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📊 Smart cleanup results: {\n  deleted: \u001b[33m3\u001b[39m,\n  retained: \u001b[33m9\u001b[39m,\n  retentionBreakdown: { immediate: \u001b[33m0\u001b[39m, debug: \u001b[33m9\u001b[39m, audit: \u001b[33m0\u001b[39m }\n}\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 smart cleanup completed\n"
                        },
                        {
                          "text": "   Deleted: 3, Retained: 9\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Failed to create user for user-login: {\n  status: \u001b[33m422\u001b[39m,\n  statusText: \u001b[32m'Unprocessable Entity'\u001b[39m,\n  error: \u001b[32m'{\"code\":422,\"error_code\":\"email_exists\",\"msg\":\"A user with this email address has already been registered\"}'\u001b[39m\n}\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-17T23:08:55.313Z",
                      "annotations": [],
                      "attachments": [],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/enhanced-user-strategy.ts",
                        "column": 15,
                        "line": 95
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f3521e5c0afb637e8e11-6ba5eb951dc2754bca97",
              "file": "auth-optimized.test.ts",
              "line": 34,
              "column": 7
            },
            {
              "title": "User Login Flow",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": -1,
                      "parallelIndex": -1,
                      "status": "skipped",
                      "duration": 0,
                      "errors": [],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:08:57.189Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "skipped"
                }
              ],
              "id": "f3521e5c0afb637e8e11-3102366ec7dd2cdacf60",
              "file": "auth-optimized.test.ts",
              "line": 64,
              "column": 7
            },
            {
              "title": "Password Reset Flow",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 4,
                      "parallelIndex": 4,
                      "status": "failed",
                      "duration": 12410,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByText(/reset link sent/i)\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByText(/reset link sent/i)\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByText(/reset link sent/i)\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByText(/reset link sent/i)\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:110:54",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts",
                          "column": 54,
                          "line": 110
                        },
                        "snippet": "  108 |     \n  109 |     // Verify success message\n> 110 |     await expect(page.getByText(/reset link sent/i)).toBeVisible();\n      |                                                      ^\n  111 |     \n  112 |     // Test back to login navigation\n  113 |     await page.getByRole('link', { name: /back to sign in/i }).click();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts",
                            "column": 54,
                            "line": 110
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByText(/reset link sent/i)\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByText(/reset link sent/i)\u001b[22m\n\n\n  108 |     \n  109 |     // Verify success message\n> 110 |     await expect(page.getByText(/reset link sent/i)).toBeVisible();\n      |                                                      ^\n  111 |     \n  112 |     // Test back to login navigation\n  113 |     await page.getByRole('link', { name: /back to sign in/i }).click();\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:110:54"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🏭 Creating TaskMaster 2 user batch: Authentication System\n"
                        },
                        {
                          "text": "   Test scenarios: user-registration, user-login, password-reset, logout-functionality, session-persistence, form-validation\n"
                        },
                        {
                          "text": "   Retention policy: debug\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-registration | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-login | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | password-reset | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | logout-functionality | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | session-persistence | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | form-validation | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created 6 TaskMaster 2 users in 1590ms\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 (Authentication) users created\n"
                        },
                        {
                          "text": "   Created 6 users with debug retention policy\n"
                        },
                        {
                          "text": "🧹 Performing smart cleanup with retention policies...\n"
                        },
                        {
                          "text": "🗑️ Deleted: <EMAIL>\n"
                        },
                        {
                          "text": "🗑️ Deleted: <EMAIL>\n"
                        },
                        {
                          "text": "📊 Smart cleanup results: {\n  deleted: \u001b[33m2\u001b[39m,\n  retained: \u001b[33m0\u001b[39m,\n  retentionBreakdown: { immediate: \u001b[33m0\u001b[39m, debug: \u001b[33m0\u001b[39m, audit: \u001b[33m0\u001b[39m }\n}\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 smart cleanup completed\n"
                        },
                        {
                          "text": "   Deleted: 2, Retained: 0\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:08:55.310Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-d4243--Task-2-Password-Reset-Flow-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-d4243--Task-2-Password-Reset-Flow-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-d4243--Task-2-Password-Reset-Flow-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts",
                        "column": 54,
                        "line": 110
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f3521e5c0afb637e8e11-1f37ef95625acd1a7102",
              "file": "auth-optimized.test.ts",
              "line": 96,
              "column": 7
            },
            {
              "title": "Logout Functionality",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 7,
                      "parallelIndex": 4,
                      "status": "failed",
                      "duration": 35399,
                      "error": {
                        "message": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'TaskMaster 2 Test User (logout-functionality)' })\u001b[22m\n",
                        "stack": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'TaskMaster 2 Test User (logout-functionality)' })\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:128:61",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts",
                          "column": 61,
                          "line": 128
                        },
                        "snippet": "  126 |     \n  127 |     // Click user profile dropdown\n> 128 |     await page.getByRole('button', { name: testUser.name }).click();\n      |                                                             ^\n  129 |     \n  130 |     // Click logout\n  131 |     await page.getByRole('menuitem', { name: 'Sign out' }).click();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts",
                            "column": 61,
                            "line": 128
                          },
                          "message": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'TaskMaster 2 Test User (logout-functionality)' })\u001b[22m\n\n\n  126 |     \n  127 |     // Click user profile dropdown\n> 128 |     await page.getByRole('button', { name: testUser.name }).click();\n      |                                                             ^\n  129 |     \n  130 |     // Click logout\n  131 |     await page.getByRole('menuitem', { name: 'Sign out' }).click();\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:128:61"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🏭 Creating TaskMaster 2 user batch: Authentication System\n"
                        },
                        {
                          "text": "   Test scenarios: user-registration, user-login, password-reset, logout-functionality, session-persistence, form-validation\n"
                        },
                        {
                          "text": "   Retention policy: debug\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-registration | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-login | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | password-reset | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | logout-functionality | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | session-persistence | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | form-validation | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created 6 TaskMaster 2 users in 1642ms\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 (Authentication) users created\n"
                        },
                        {
                          "text": "   Created 6 users with debug retention policy\n"
                        },
                        {
                          "text": "🧹 Performing smart cleanup with retention policies...\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📊 Smart cleanup results: {\n  deleted: \u001b[33m0\u001b[39m,\n  retained: \u001b[33m12\u001b[39m,\n  retentionBreakdown: { immediate: \u001b[33m0\u001b[39m, debug: \u001b[33m12\u001b[39m, audit: \u001b[33m0\u001b[39m }\n}\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 smart cleanup completed\n"
                        },
                        {
                          "text": "   Deleted: 0, Retained: 12\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:10.536Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-3217d-Task-2-Logout-Functionality-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-3217d-Task-2-Logout-Functionality-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-3217d-Task-2-Logout-Functionality-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts",
                        "column": 61,
                        "line": 128
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f3521e5c0afb637e8e11-1f4eebfc085c3be95a6a",
              "file": "auth-optimized.test.ts",
              "line": 117,
              "column": 7
            },
            {
              "title": "Session Persistence",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 1,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 12043,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at ListlessTestHelpers.login (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts:213:29)\n    at ListlessTestHelpers.loginWithTestUser (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts:220:5)\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:146:5",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts",
                          "column": 29,
                          "line": 213
                        },
                        "snippet": "\u001b[90m   at \u001b[39mtest-utils.ts:213\n\n  211 |     await this.page.getByLabel('Password').fill(password);\n  212 |     await this.page.getByRole('button', { name: 'Sign in' }).click();\n> 213 |     await expect(this.page).toHaveURL('/dashboard');\n      |                             ^\n  214 |   }\n  215 |\n  216 |   /**"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts",
                            "column": 29,
                            "line": 213
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n   at test-utils.ts:213\n\n  211 |     await this.page.getByLabel('Password').fill(password);\n  212 |     await this.page.getByRole('button', { name: 'Sign in' }).click();\n> 213 |     await expect(this.page).toHaveURL('/dashboard');\n      |                             ^\n  214 |   }\n  215 |\n  216 |   /**\n    at ListlessTestHelpers.login (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts:213:29)\n    at ListlessTestHelpers.loginWithTestUser (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts:220:5)\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:146:5"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🏭 Creating TaskMaster 2 user batch: Authentication System\n"
                        },
                        {
                          "text": "   Test scenarios: user-registration, user-login, password-reset, logout-functionality, session-persistence, form-validation\n"
                        },
                        {
                          "text": "   Retention policy: debug\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-registration | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-login | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | password-reset | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | logout-functionality | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | session-persistence | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | form-validation | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created 6 TaskMaster 2 users in 2166ms\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 (Authentication) users created\n"
                        },
                        {
                          "text": "   Created 6 users with debug retention policy\n"
                        },
                        {
                          "text": "🧹 Performing smart cleanup with retention policies...\n"
                        },
                        {
                          "text": "📊 Smart cleanup results: {\n  deleted: \u001b[33m0\u001b[39m,\n  retained: \u001b[33m0\u001b[39m,\n  retentionBreakdown: { immediate: \u001b[33m0\u001b[39m, debug: \u001b[33m0\u001b[39m, audit: \u001b[33m0\u001b[39m }\n}\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 smart cleanup completed\n"
                        },
                        {
                          "text": "   Deleted: 0, Retained: 0\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Failed <NAME_EMAIL>: Error: Failed to delete user cf8a938f-1801-4b6c-be37-36dc9812b451: Not Found\n    at Function.deleteUser \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/enhanced-user-strategy.ts:315:13\u001b[90m)\u001b[39m\n\u001b[90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)\u001b[39m\n    at Function.performSmartCleanup \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/enhanced-user-strategy.ts:234:11\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/auth-optimized.test.ts:24:27\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:284:11\n    at TimeoutManager.withRunnable \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/timeoutManager.js:67:14\u001b[90m)\u001b[39m\n    at TestInfoImpl._runWithTimeout \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:282:7\u001b[90m)\u001b[39m\n    at FixtureRunner.resolveParametersAndRunFunction \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/fixtureRunner.js:223:5\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:423:13\n    at TestInfoImpl._runAsStep \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:273:7\u001b[90m)\u001b[39m\n    at WorkerMain._runAllHooksForSuite \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:418:9\u001b[90m)\u001b[39m\n    at WorkerMain._runAfterAllHooksForSuite \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:448:5\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:334:13\n    at TestInfoImpl._runAsStep \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:273:7\u001b[90m)\u001b[39m\n    at WorkerMain._runTest \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:312:5\u001b[90m)\u001b[39m\n    at WorkerMain.runTestGroup \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:193:11\u001b[90m)\u001b[39m\n    at process.<anonymous> \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/common/process.js:65:22\u001b[90m)\u001b[39m\n"
                        },
                        {
                          "text": "Failed <NAME_EMAIL>: Error: Failed to delete user 7640b524-79e4-4ee6-93df-ad529bc06e52: Not Found\n    at Function.deleteUser \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/enhanced-user-strategy.ts:315:13\u001b[90m)\u001b[39m\n\u001b[90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)\u001b[39m\n    at Function.performSmartCleanup \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/enhanced-user-strategy.ts:234:11\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/auth-optimized.test.ts:24:27\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:284:11\n    at TimeoutManager.withRunnable \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/timeoutManager.js:67:14\u001b[90m)\u001b[39m\n    at TestInfoImpl._runWithTimeout \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:282:7\u001b[90m)\u001b[39m\n    at FixtureRunner.resolveParametersAndRunFunction \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/fixtureRunner.js:223:5\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:423:13\n    at TestInfoImpl._runAsStep \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:273:7\u001b[90m)\u001b[39m\n    at WorkerMain._runAllHooksForSuite \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:418:9\u001b[90m)\u001b[39m\n    at WorkerMain._runAfterAllHooksForSuite \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:448:5\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:334:13\n    at TestInfoImpl._runAsStep \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:273:7\u001b[90m)\u001b[39m\n    at WorkerMain._runTest \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:312:5\u001b[90m)\u001b[39m\n    at WorkerMain.runTestGroup \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:193:11\u001b[90m)\u001b[39m\n    at process.<anonymous> \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/common/process.js:65:22\u001b[90m)\u001b[39m\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-17T23:08:55.318Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-5811e--Task-2-Session-Persistence-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-5811e--Task-2-Session-Persistence-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-5811e--Task-2-Session-Persistence-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts",
                        "column": 29,
                        "line": 213
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f3521e5c0afb637e8e11-3b6a68855bfa3340aac5",
              "file": "auth-optimized.test.ts",
              "line": 141,
              "column": 7
            },
            {
              "title": "Form Validation",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 8,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 31024,
                      "error": {
                        "message": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'Create account' })\u001b[22m\n\u001b[2m    - locator resolved to <button disabled type=\"submit\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full\">Create account</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    57 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not enabled\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n",
                        "stack": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'Create account' })\u001b[22m\n\u001b[2m    - locator resolved to <button disabled type=\"submit\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full\">Create account</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    57 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not enabled\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:169:64",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts",
                          "column": 64,
                          "line": 169
                        },
                        "snippet": "  167 |     \n  168 |     // Test empty form submission\n> 169 |     await page.getByRole('button', { name: 'Create account' }).click();\n      |                                                                ^\n  170 |     await expect(page.getByText(/required/i).first()).toBeVisible();\n  171 |     \n  172 |     // Test invalid email format"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts",
                            "column": 64,
                            "line": 169
                          },
                          "message": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'Create account' })\u001b[22m\n\u001b[2m    - locator resolved to <button disabled type=\"submit\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full\">Create account</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    57 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not enabled\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n\n  167 |     \n  168 |     // Test empty form submission\n> 169 |     await page.getByRole('button', { name: 'Create account' }).click();\n      |                                                                ^\n  170 |     await expect(page.getByText(/required/i).first()).toBeVisible();\n  171 |     \n  172 |     // Test invalid email format\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:169:64"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🏭 Creating TaskMaster 2 user batch: Authentication System\n"
                        },
                        {
                          "text": "   Test scenarios: user-registration, user-login, password-reset, logout-functionality, session-persistence, form-validation\n"
                        },
                        {
                          "text": "   Retention policy: debug\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-registration | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-login | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | password-reset | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | logout-functionality | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | session-persistence | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | form-validation | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created 6 TaskMaster 2 users in 1966ms\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 (Authentication) users created\n"
                        },
                        {
                          "text": "   Created 6 users with debug retention policy\n"
                        },
                        {
                          "text": "🧹 Performing smart cleanup with retention policies...\n"
                        },
                        {
                          "text": "🗑️ Deleted: <EMAIL>\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "🗑️ Deleted: <EMAIL>\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "🗑️ Deleted: <EMAIL>\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "🗑️ Deleted: <EMAIL>\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📌 Retaining: <EMAIL> (debug)\n"
                        },
                        {
                          "text": "📊 Smart cleanup results: {\n  deleted: \u001b[33m4\u001b[39m,\n  retained: \u001b[33m12\u001b[39m,\n  retentionBreakdown: { immediate: \u001b[33m0\u001b[39m, debug: \u001b[33m12\u001b[39m, audit: \u001b[33m0\u001b[39m }\n}\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 smart cleanup completed\n"
                        },
                        {
                          "text": "   Deleted: 4, Retained: 12\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:10.909Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-db610-ster-Task-2-Form-Validation-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-db610-ster-Task-2-Form-Validation-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-db610-ster-Task-2-Form-Validation-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts",
                        "column": 64,
                        "line": 169
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f3521e5c0afb637e8e11-56cefc3b4396e52302a0",
              "file": "auth-optimized.test.ts",
              "line": 162,
              "column": 7
            },
            {
              "title": "Verify User Isolation",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 12284,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at ListlessTestHelpers.login (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts:213:29)\n    at ListlessTestHelpers.loginWithTestUser (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts:220:5)\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:208:7",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts",
                          "column": 29,
                          "line": 213
                        },
                        "snippet": "\u001b[90m   at \u001b[39mtest-utils.ts:213\n\n  211 |     await this.page.getByLabel('Password').fill(password);\n  212 |     await this.page.getByRole('button', { name: 'Sign in' }).click();\n> 213 |     await expect(this.page).toHaveURL('/dashboard');\n      |                             ^\n  214 |   }\n  215 |\n  216 |   /**"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts",
                            "column": 29,
                            "line": 213
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n   at test-utils.ts:213\n\n  211 |     await this.page.getByLabel('Password').fill(password);\n  212 |     await this.page.getByRole('button', { name: 'Sign in' }).click();\n> 213 |     await expect(this.page).toHaveURL('/dashboard');\n      |                             ^\n  214 |   }\n  215 |\n  216 |   /**\n    at ListlessTestHelpers.login (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts:213:29)\n    at ListlessTestHelpers.loginWithTestUser (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts:220:5)\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:208:7"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🏭 Creating TaskMaster 2 user batch: Authentication System\n"
                        },
                        {
                          "text": "   Test scenarios: user-registration, user-login, password-reset, logout-functionality, session-persistence, form-validation\n"
                        },
                        {
                          "text": "   Retention policy: debug\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-registration | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | user-login | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | password-reset | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | logout-functionality | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | session-persistence | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created: Task 2: Authentication System | form-validation | <EMAIL>\n"
                        },
                        {
                          "text": "✅ Created 6 TaskMaster 2 users in 1768ms\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 (Authentication) users created\n"
                        },
                        {
                          "text": "   Created 6 users with debug retention policy\n"
                        },
                        {
                          "text": "🔍 Verifying isolation for: Task 2: Authentication System | user-registration | <EMAIL>\n"
                        },
                        {
                          "text": "🧹 Performing smart cleanup with retention policies...\n"
                        },
                        {
                          "text": "📊 Smart cleanup results: {\n  deleted: \u001b[33m0\u001b[39m,\n  retained: \u001b[33m0\u001b[39m,\n  retentionBreakdown: { immediate: \u001b[33m0\u001b[39m, debug: \u001b[33m0\u001b[39m, audit: \u001b[33m0\u001b[39m }\n}\n"
                        },
                        {
                          "text": "✅ TaskMaster Task 2 smart cleanup completed\n"
                        },
                        {
                          "text": "   Deleted: 0, Retained: 0\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Failed <NAME_EMAIL>: Error: Failed to delete user cf8a938f-1801-4b6c-be37-36dc9812b451: Not Found\n    at Function.deleteUser \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/enhanced-user-strategy.ts:315:13\u001b[90m)\u001b[39m\n\u001b[90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)\u001b[39m\n    at Function.performSmartCleanup \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/enhanced-user-strategy.ts:234:11\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/auth-optimized.test.ts:24:27\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:284:11\n    at TimeoutManager.withRunnable \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/timeoutManager.js:67:14\u001b[90m)\u001b[39m\n    at TestInfoImpl._runWithTimeout \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:282:7\u001b[90m)\u001b[39m\n    at FixtureRunner.resolveParametersAndRunFunction \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/fixtureRunner.js:223:5\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:423:13\n    at TestInfoImpl._runAsStep \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:273:7\u001b[90m)\u001b[39m\n    at WorkerMain._runAllHooksForSuite \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:418:9\u001b[90m)\u001b[39m\n    at WorkerMain._runAfterAllHooksForSuite \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:448:5\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:334:13\n    at TestInfoImpl._runAsStep \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:273:7\u001b[90m)\u001b[39m\n    at WorkerMain._runTest \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:312:5\u001b[90m)\u001b[39m\n    at WorkerMain.runTestGroup \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:193:11\u001b[90m)\u001b[39m\n    at process.<anonymous> \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/common/process.js:65:22\u001b[90m)\u001b[39m\n"
                        },
                        {
                          "text": "Failed <NAME_EMAIL>: Error: Failed to delete user 7640b524-79e4-4ee6-93df-ad529bc06e52: Not Found\n    at Function.deleteUser \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/enhanced-user-strategy.ts:315:13\u001b[90m)\u001b[39m\n\u001b[90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)\u001b[39m\n    at Function.performSmartCleanup \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/enhanced-user-strategy.ts:234:11\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mtests/auth-optimized.test.ts:24:27\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:284:11\n    at TimeoutManager.withRunnable \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/timeoutManager.js:67:14\u001b[90m)\u001b[39m\n    at TestInfoImpl._runWithTimeout \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:282:7\u001b[90m)\u001b[39m\n    at FixtureRunner.resolveParametersAndRunFunction \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/fixtureRunner.js:223:5\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:423:13\n    at TestInfoImpl._runAsStep \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:273:7\u001b[90m)\u001b[39m\n    at WorkerMain._runAllHooksForSuite \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:418:9\u001b[90m)\u001b[39m\n    at WorkerMain._runAfterAllHooksForSuite \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:448:5\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:334:13\n    at TestInfoImpl._runAsStep \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/testInfo.js:273:7\u001b[90m)\u001b[39m\n    at WorkerMain._runTest \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:312:5\u001b[90m)\u001b[39m\n    at WorkerMain.runTestGroup \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/worker/workerMain.js:193:11\u001b[90m)\u001b[39m\n    at process.<anonymous> \u001b[90m(/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/\u001b[39mnode_modules/\u001b[4mplaywright\u001b[24m/lib/common/process.js:65:22\u001b[90m)\u001b[39m\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-17T23:08:55.322Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-d1eca-ask-2-Verify-User-Isolation-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-d1eca-ask-2-Verify-User-Isolation-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-optimized-Authenticat-d1eca-ask-2-Verify-User-Isolation-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-utils.ts",
                        "column": 29,
                        "line": 213
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f3521e5c0afb637e8e11-3a485be46b7ab1516889",
              "file": "auth-optimized.test.ts",
              "line": 201,
              "column": 7
            }
          ]
        },
        {
          "title": "Performance Validation",
          "file": "auth-optimized.test.ts",
          "line": 225,
          "column": 6,
          "specs": [
            {
              "title": "Measure User Creation Performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 2,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 314,
                      "error": {
                        "message": "TypeError: Cannot read properties of undefined (reading 'id')",
                        "stack": "TypeError: Cannot read properties of undefined (reading 'id')\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/optimized-user-strategy.ts:67:29\n    at Function.createUserBatch (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/optimized-user-strategy.ts:75:19)\n    at TestSuiteHelper.setup (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/optimized-user-strategy.ts:194:18)\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:231:5",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/optimized-user-strategy.ts",
                          "column": 29,
                          "line": 67
                        },
                        "snippet": "\u001b[90m   at \u001b[39moptimized-user-strategy.ts:67\n\n  65 |\n  66 |       const result = await response.json();\n> 67 |       user.id = result.user.id;\n     |                             ^\n  68 |       \n  69 |       // Track created user for cleanup\n  70 |       this.createdUserIds.add(user.id);"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/optimized-user-strategy.ts",
                            "column": 29,
                            "line": 67
                          },
                          "message": "TypeError: Cannot read properties of undefined (reading 'id')\n\n   at optimized-user-strategy.ts:67\n\n  65 |\n  66 |       const result = await response.json();\n> 67 |       user.id = result.user.id;\n     |                             ^\n  68 |       \n  69 |       // Track created user for cleanup\n  70 |       this.createdUserIds.add(user.id);\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/optimized-user-strategy.ts:67:29\n    at Function.createUserBatch (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/optimized-user-strategy.ts:75:19)\n    at TestSuiteHelper.setup (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/optimized-user-strategy.ts:194:18)\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth-optimized.test.ts:231:5"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🏭 Creating batch of 6 users for suite: auth\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:08:55.312Z",
                      "annotations": [],
                      "attachments": [],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/optimized-user-strategy.ts",
                        "column": 29,
                        "line": 67
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f3521e5c0afb637e8e11-699865c119e719fae96c",
              "file": "auth-optimized.test.ts",
              "line": 226,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "auth.test.ts",
      "file": "auth.test.ts",
      "column": 0,
      "line": 0,
      "specs": [
        {
          "title": "global setup",
          "ok": true,
          "tags": [],
          "tests": [
            {
              "timeout": 60000,
              "annotations": [],
              "expectedStatus": "passed",
              "projectId": "chromium",
              "projectName": "chromium",
              "results": [
                {
                  "workerIndex": 5,
                  "parallelIndex": 2,
                  "status": "passed",
                  "duration": 2768,
                  "errors": [],
                  "stdout": [
                    {
                      "text": "🚀 Starting Playwright test setup for Listless...\n"
                    },
                    {
                      "text": "🧹 Cleaning up 11 test users...\n"
                    },
                    {
                      "text": "✅ Deleted test user: 77bc0fc2-02ba-4941-8672-c87a60109348\n"
                    },
                    {
                      "text": "✅ Deleted test user: 5836ac44-ff72-441b-a4ea-54e55c707afd\n"
                    },
                    {
                      "text": "✅ Deleted test user: 9e5a4da1-ba60-4c62-ac7d-34c9dae8ffab\n"
                    },
                    {
                      "text": "✅ Deleted test user: fd25bc25-80c7-4528-a2cf-de69b4d23554\n"
                    },
                    {
                      "text": "✅ Deleted test user: eb6b53e5-f778-4717-8405-b8d33332ea2b\n"
                    },
                    {
                      "text": "✅ Deleted test user: 283592d0-58b0-49b7-8d9f-5e542d4b0808\n"
                    },
                    {
                      "text": "✅ Deleted test user: 9bf1bd4b-885d-4be7-9340-29afe8a74db0\n"
                    },
                    {
                      "text": "✅ Deleted test user: 06063c8c-8d4d-4964-929e-8cea5b876b7e\n"
                    },
                    {
                      "text": "✅ Test user cleanup completed\n"
                    },
                    {
                      "text": "✅ Global test setup completed\n"
                    }
                  ],
                  "stderr": [
                    {
                      "text": "Failed to delete test user e7c01b13-b54a-4832-91aa-d55e8e21dfc6: Not Found\n"
                    },
                    {
                      "text": "Failed to delete test user 669b83c5-0f5c-4625-8212-1eb5e5ff6169: Not Found\n"
                    },
                    {
                      "text": "Failed to delete test user 82d47eac-e20e-430a-8485-d9e7a73f33be: Not Found\n"
                    }
                  ],
                  "retry": 0,
                  "startTime": "2025-06-17T23:08:56.016Z",
                  "annotations": [],
                  "attachments": []
                }
              ],
              "status": "expected"
            }
          ],
          "id": "f0c50cfc8142f74e2e08-12e85f7d3daebbbb7aa2",
          "file": "test-setup.ts",
          "line": 103,
          "column": 6
        },
        {
          "title": "global teardown",
          "ok": true,
          "tags": [],
          "tests": [
            {
              "timeout": 60000,
              "annotations": [],
              "expectedStatus": "passed",
              "projectId": "chromium",
              "projectName": "chromium",
              "results": [
                {
                  "workerIndex": 6,
                  "parallelIndex": 3,
                  "status": "passed",
                  "duration": 2414,
                  "errors": [],
                  "stdout": [
                    {
                      "text": "🧹 Starting Playwright test teardown...\n"
                    },
                    {
                      "text": "🧹 Cleaning up 14 test users...\n"
                    },
                    {
                      "text": "✅ Deleted test user: 1147d587-4f5b-4b83-810b-e2b03c5a7b04\n"
                    },
                    {
                      "text": "✅ Deleted test user: 6b67f084-579b-4fd8-b36b-2b228c0c0b71\n"
                    },
                    {
                      "text": "✅ Deleted test user: c26e4560-55c8-4c5b-baa1-35aca95baea2\n"
                    },
                    {
                      "text": "✅ Deleted test user: b55e59dd-488b-45f4-b5ec-cce1af8b0426\n"
                    },
                    {
                      "text": "✅ Deleted test user: b9fcd53d-56c9-4f54-a9f0-6a6e5fce71ed\n"
                    },
                    {
                      "text": "✅ Deleted test user: 8e1045e2-7c19-43ce-a9d4-d187958243ac\n"
                    },
                    {
                      "text": "✅ Deleted test user: 5c2bc50e-0619-4137-9526-c0acf9dff65a\n"
                    },
                    {
                      "text": "✅ Deleted test user: 92045abd-b1af-4b4e-af1e-a3d83bc22795\n"
                    },
                    {
                      "text": "✅ Deleted test user: ea312ec9-0a4d-4848-a205-67659a2a35ff\n"
                    },
                    {
                      "text": "✅ Deleted test user: 97618953-f7ed-454e-a7cd-c808f2921bee\n"
                    },
                    {
                      "text": "✅ Deleted test user: 353a3d6c-a26f-47e6-99e0-39a3f48009de\n"
                    },
                    {
                      "text": "✅ Test user cleanup completed\n"
                    },
                    {
                      "text": "✅ Global test teardown completed\n"
                    }
                  ],
                  "stderr": [
                    {
                      "text": "Failed to delete test user 283592d0-58b0-49b7-8d9f-5e542d4b0808: Not Found\n"
                    },
                    {
                      "text": "Failed to delete test user 9bf1bd4b-885d-4be7-9340-29afe8a74db0: Not Found\n"
                    },
                    {
                      "text": "Failed to delete test user 06063c8c-8d4d-4964-929e-8cea5b876b7e: Not Found\n"
                    }
                  ],
                  "retry": 0,
                  "startTime": "2025-06-17T23:08:57.606Z",
                  "annotations": [],
                  "attachments": []
                }
              ],
              "status": "expected"
            }
          ],
          "id": "f0c50cfc8142f74e2e08-2dd778d897b6e1121e5b",
          "file": "test-setup.ts",
          "line": 128,
          "column": 6
        }
      ],
      "suites": [
        {
          "title": "Authentication Flow",
          "file": "auth.test.ts",
          "line": 7,
          "column": 6,
          "specs": [
            {
              "title": "User Registration Flow",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 5,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 11808,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/\\/auth\\/verify-email/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/auth/signup?error=Email%20address%20%22tm0-generic-unknown-0-1750201739893%40listless-testing.local%22%20is%20invalid\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/signup\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/signup?error=Email%20address%20%22tm0-generic-unknown-0-1750201739893%40listless-testing.local%22%20is%20invalid\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/\\/auth\\/verify-email/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/auth/signup?error=Email%20address%20%22tm0-generic-unknown-0-1750201739893%40listless-testing.local%22%20is%20invalid\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/signup\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/signup?error=Email%20address%20%22tm0-generic-unknown-0-1750201739893%40listless-testing.local%22%20is%20invalid\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:46:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                          "column": 24,
                          "line": 46
                        },
                        "snippet": "  44 |     \n  45 |     // Verify redirect to email verification page\n> 46 |     await expect(page).toHaveURL(/\\/auth\\/verify-email/);\n     |                        ^\n  47 |     await expect(page.getByText(/verification email sent/i)).toBeVisible();\n  48 |   });\n  49 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                            "column": 24,
                            "line": 46
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/\\/auth\\/verify-email/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/auth/signup?error=Email%20address%20%22tm0-generic-unknown-0-1750201739893%40listless-testing.local%22%20is%20invalid\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/signup\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/signup?error=Email%20address%20%22tm0-generic-unknown-0-1750201739893%40listless-testing.local%22%20is%20invalid\"\u001b[22m\n\n\n  44 |     \n  45 |     // Verify redirect to email verification page\n> 46 |     await expect(page).toHaveURL(/\\/auth\\/verify-email/);\n     |                        ^\n  47 |     await expect(page.getByText(/verification email sent/i)).toBeVisible();\n  48 |   });\n  49 |\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:46:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "✅ Created verified test user: <EMAIL>\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:08:58.791Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-User-Registration-Flow-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-User-Registration-Flow-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-User-Registration-Flow-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                        "column": 24,
                        "line": 46
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f0c50cfc8142f74e2e08-111e16e2cafa440b94f2",
              "file": "auth.test.ts",
              "line": 21,
              "column": 7
            },
            {
              "title": "User Login Flow",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 6,
                      "parallelIndex": 3,
                      "status": "failed",
                      "duration": 11172,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/Sign in/i\u001b[39m\nReceived string:  \u001b[31m\"AI Task Management\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    14 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"AI Task Management\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/Sign in/i\u001b[39m\nReceived string:  \u001b[31m\"AI Task Management\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    14 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"AI Task Management\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:55:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                          "column": 24,
                          "line": 55
                        },
                        "snippet": "  53 |     \n  54 |     // Verify login page loads\n> 55 |     await expect(page).toHaveTitle(/Sign in/i);\n     |                        ^\n  56 |     await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible();\n  57 |     \n  58 |     // Fill login form"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                            "column": 24,
                            "line": 55
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/Sign in/i\u001b[39m\nReceived string:  \u001b[31m\"AI Task Management\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    14 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"AI Task Management\"\u001b[22m\n\n\n  53 |     \n  54 |     // Verify login page loads\n> 55 |     await expect(page).toHaveTitle(/Sign in/i);\n     |                        ^\n  56 |     await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible();\n  57 |     \n  58 |     // Fill login form\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:55:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "✅ Created verified test user: <EMAIL>\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:00.036Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-User-Login-Flow-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-User-Login-Flow-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-User-Login-Flow-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                        "column": 24,
                        "line": 55
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f0c50cfc8142f74e2e08-52211117800fe15e0260",
              "file": "auth.test.ts",
              "line": 50,
              "column": 7
            },
            {
              "title": "Password Reset Flow",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 9,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 12081,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByText(/reset link sent/i)\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByText(/reset link sent/i)\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByText(/reset link sent/i)\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByText(/reset link sent/i)\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:90:54",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                          "column": 54,
                          "line": 90
                        },
                        "snippet": "  88 |     \n  89 |     // Verify success message\n> 90 |     await expect(page.getByText(/reset link sent/i)).toBeVisible();\n     |                                                      ^\n  91 |     \n  92 |     // Test back to login navigation\n  93 |     await page.getByRole('link', { name: /back to sign in/i }).click();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                            "column": 54,
                            "line": 90
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByText(/reset link sent/i)\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByText(/reset link sent/i)\u001b[22m\n\n\n  88 |     \n  89 |     // Verify success message\n> 90 |     await expect(page.getByText(/reset link sent/i)).toBeVisible();\n     |                                                      ^\n  91 |     \n  92 |     // Test back to login navigation\n  93 |     await page.getByRole('link', { name: /back to sign in/i }).click();\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:90:54"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "✅ Created verified test user: <EMAIL>\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:11.030Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Password-Reset-Flow-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Password-Reset-Flow-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Password-Reset-Flow-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                        "column": 54,
                        "line": 90
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f0c50cfc8142f74e2e08-129f53b481bf09b00163",
              "file": "auth.test.ts",
              "line": 79,
              "column": 7
            },
            {
              "title": "Logout Functionality",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 10,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 36918,
                      "error": {
                        "message": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'TaskMaster 0 Test User (unknown)' })\u001b[22m\n\u001b[2m    3 × waiting for\" http://localhost:3000/auth/login\" navigation to finish...\u001b[22m\n\u001b[2m      - navigated to \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    - waiting for\" http://localhost:3000/auth/login\" navigation to finish...\u001b[22m\n",
                        "stack": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'TaskMaster 0 Test User (unknown)' })\u001b[22m\n\u001b[2m    3 × waiting for\" http://localhost:3000/auth/login\" navigation to finish...\u001b[22m\n\u001b[2m      - navigated to \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    - waiting for\" http://localhost:3000/auth/login\" navigation to finish...\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:108:61",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                          "column": 61,
                          "line": 108
                        },
                        "snippet": "  106 |     \n  107 |     // Click user profile dropdown\n> 108 |     await page.getByRole('button', { name: testUser.name }).click();\n      |                                                             ^\n  109 |     \n  110 |     // Click logout\n  111 |     await page.getByRole('menuitem', { name: 'Sign out' }).click();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                            "column": 61,
                            "line": 108
                          },
                          "message": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'TaskMaster 0 Test User (unknown)' })\u001b[22m\n\u001b[2m    3 × waiting for\" http://localhost:3000/auth/login\" navigation to finish...\u001b[22m\n\u001b[2m      - navigated to \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    - waiting for\" http://localhost:3000/auth/login\" navigation to finish...\u001b[22m\n\n\n  106 |     \n  107 |     // Click user profile dropdown\n> 108 |     await page.getByRole('button', { name: testUser.name }).click();\n      |                                                             ^\n  109 |     \n  110 |     // Click logout\n  111 |     await page.getByRole('menuitem', { name: 'Sign out' }).click();\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:108:61"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "✅ Created verified test user: <EMAIL>\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:11.153Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Logout-Functionality-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Logout-Functionality-chromium/video.webm"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                        "column": 61,
                        "line": 108
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f0c50cfc8142f74e2e08-c60be37b363eee54c20b",
              "file": "auth.test.ts",
              "line": 97,
              "column": 7
            },
            {
              "title": "Session Persistence",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 11,
                      "parallelIndex": 3,
                      "status": "failed",
                      "duration": 18223,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByText('TaskMaster 0 Test User (unknown)')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByText('TaskMaster 0 Test User (unknown)')\u001b[22m\n\u001b[2m    - waiting for\" http://localhost:3000/dashboard\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"http://localhost:3000/dashboard\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByText('TaskMaster 0 Test User (unknown)')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByText('TaskMaster 0 Test User (unknown)')\u001b[22m\n\u001b[2m    - waiting for\" http://localhost:3000/dashboard\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"http://localhost:3000/dashboard\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:139:52",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                          "column": 52,
                          "line": 139
                        },
                        "snippet": "  137 |     await newPage.goto('http://localhost:3000/dashboard');\n  138 |     await expect(newPage).toHaveURL('/dashboard');\n> 139 |     await expect(newPage.getByText(testUser.name)).toBeVisible();\n      |                                                    ^\n  140 |   });\n  141 |\n  142 |   test('Form Validation', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                            "column": 52,
                            "line": 139
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByText('TaskMaster 0 Test User (unknown)')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByText('TaskMaster 0 Test User (unknown)')\u001b[22m\n\u001b[2m    - waiting for\" http://localhost:3000/dashboard\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"http://localhost:3000/dashboard\"\u001b[22m\n\n\n  137 |     await newPage.goto('http://localhost:3000/dashboard');\n  138 |     await expect(newPage).toHaveURL('/dashboard');\n> 139 |     await expect(newPage.getByText(testUser.name)).toBeVisible();\n      |                                                    ^\n  140 |   });\n  141 |\n  142 |   test('Form Validation', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:139:52"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "✅ Created verified test user: <EMAIL>\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:11.689Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Session-Persistence-chromium/test-failed-1.png"
                        },
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Session-Persistence-chromium/test-failed-2.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Session-Persistence-chromium/video-1.webm"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Session-Persistence-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Session-Persistence-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                        "column": 52,
                        "line": 139
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f0c50cfc8142f74e2e08-a458d832d6a5cb7d6326",
              "file": "auth.test.ts",
              "line": 121,
              "column": 7
            },
            {
              "title": "Form Validation",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 12,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 31583,
                      "error": {
                        "message": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'Create account' })\u001b[22m\n\u001b[2m    - locator resolved to <button disabled type=\"submit\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full\">Create account</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    57 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not enabled\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n",
                        "stack": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'Create account' })\u001b[22m\n\u001b[2m    - locator resolved to <button disabled type=\"submit\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full\">Create account</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    57 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not enabled\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:146:64",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                          "column": 64,
                          "line": 146
                        },
                        "snippet": "  144 |     \n  145 |     // Test empty form submission\n> 146 |     await page.getByRole('button', { name: 'Create account' }).click();\n      |                                                                ^\n  147 |     await expect(page.getByText(/required/i).first()).toBeVisible();\n  148 |     \n  149 |     // Test invalid email format"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                            "column": 64,
                            "line": 146
                          },
                          "message": "TimeoutError: locator.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'Create account' })\u001b[22m\n\u001b[2m    - locator resolved to <button disabled type=\"submit\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full\">Create account</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    57 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not enabled\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n\n  144 |     \n  145 |     // Test empty form submission\n> 146 |     await page.getByRole('button', { name: 'Create account' }).click();\n      |                                                                ^\n  147 |     await expect(page.getByText(/required/i).first()).toBeVisible();\n  148 |     \n  149 |     // Test invalid email format\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts:146:64"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "✅ Created verified test user: <EMAIL>\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:23.727Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Form-Validation-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Form-Validation-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/auth-Authentication-Flow-Form-Validation-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/auth.test.ts",
                        "column": 64,
                        "line": 146
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "f0c50cfc8142f74e2e08-b7a0905dc809d14bf11a",
              "file": "auth.test.ts",
              "line": 142,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "project-area-management.test.ts",
      "file": "project-area-management.test.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Project and Area Management",
          "file": "project-area-management.test.ts",
          "line": 3,
          "column": 6,
          "specs": [
            {
              "title": "Create New Project",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 13,
                      "parallelIndex": 3,
                      "status": "failed",
                      "duration": 11205,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                          "column": 24,
                          "line": 10
                        },
                        "snippet": "   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                            "column": 24,
                            "line": 10
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:30.458Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-4d0e0-nagement-Create-New-Project-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-4d0e0-nagement-Create-New-Project-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-4d0e0-nagement-Create-New-Project-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                        "column": 24,
                        "line": 10
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "df28d947d3ea219b9f05-c684ef185894aa2241b7",
              "file": "project-area-management.test.ts",
              "line": 13,
              "column": 7
            },
            {
              "title": "Create New Area",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 14,
                      "parallelIndex": 3,
                      "status": "failed",
                      "duration": 10797,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                          "column": 24,
                          "line": 10
                        },
                        "snippet": "   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                            "column": 24,
                            "line": 10
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:42.260Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-5192a--Management-Create-New-Area-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-5192a--Management-Create-New-Area-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-5192a--Management-Create-New-Area-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                        "column": 24,
                        "line": 10
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "df28d947d3ea219b9f05-7b5955dd060ebc92a9c1",
              "file": "project-area-management.test.ts",
              "line": 35,
              "column": 7
            },
            {
              "title": "Convert Task to Project",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 15,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 10834,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                          "column": 24,
                          "line": 10
                        },
                        "snippet": "   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                            "column": 24,
                            "line": 10
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:45.714Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-1e706-ent-Convert-Task-to-Project-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-1e706-ent-Convert-Task-to-Project-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-1e706-ent-Convert-Task-to-Project-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                        "column": 24,
                        "line": 10
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "df28d947d3ea219b9f05-a2577cdd0d4d5fab867e",
              "file": "project-area-management.test.ts",
              "line": 54,
              "column": 7
            },
            {
              "title": "Project Detail View Management",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 16,
                      "parallelIndex": 4,
                      "status": "failed",
                      "duration": 10768,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                          "column": 24,
                          "line": 10
                        },
                        "snippet": "   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                            "column": 24,
                            "line": 10
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:48.592Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-73d2f-ject-Detail-View-Management-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-73d2f-ject-Detail-View-Management-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-73d2f-ject-Detail-View-Management-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                        "column": 24,
                        "line": 10
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "df28d947d3ea219b9f05-7dfa0d2c357a6c7d2292",
              "file": "project-area-management.test.ts",
              "line": 87,
              "column": 7
            },
            {
              "title": "Area Expansion and Collapse",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 17,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 10736,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                          "column": 24,
                          "line": 10
                        },
                        "snippet": "   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                            "column": 24,
                            "line": 10
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:48.633Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-cc570-Area-Expansion-and-Collapse-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-cc570-Area-Expansion-and-Collapse-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-cc570-Area-Expansion-and-Collapse-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                        "column": 24,
                        "line": 10
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "df28d947d3ea219b9f05-389d94e8f5d9ab1e5578",
              "file": "project-area-management.test.ts",
              "line": 125,
              "column": 7
            },
            {
              "title": "Project and Area Deletion with Cascading Effects",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 18,
                      "parallelIndex": 3,
                      "status": "failed",
                      "duration": 10809,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                          "column": 24,
                          "line": 10
                        },
                        "snippet": "   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                            "column": 24,
                            "line": 10
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:53.649Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-15887-tion-with-Cascading-Effects-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-15887-tion-with-Cascading-Effects-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-15887-tion-with-Cascading-Effects-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                        "column": 24,
                        "line": 10
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "df28d947d3ea219b9f05-f01c81ac8dcd358cc7e9",
              "file": "project-area-management.test.ts",
              "line": 157,
              "column": 7
            },
            {
              "title": "Project Organization and Movement",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 19,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 10655,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                          "column": 24,
                          "line": 10
                        },
                        "snippet": "   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                            "column": 24,
                            "line": 10
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n   8 |     await page.getByLabel('Password').fill('TestPassword123!');\n   9 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 10 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  11 |   });\n  12 |\n  13 |   test('Create New Project', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts:10:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:55.867Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-6823a-t-Organization-and-Movement-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-6823a-t-Organization-and-Movement-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/project-area-management-Pr-6823a-t-Organization-and-Movement-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/project-area-management.test.ts",
                        "column": 24,
                        "line": 10
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "df28d947d3ea219b9f05-80642db561c538df3098",
              "file": "project-area-management.test.ts",
              "line": 198,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "task-management.test.ts",
      "file": "task-management.test.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Task Management",
          "file": "task-management.test.ts",
          "line": 10,
          "column": 6,
          "specs": [
            {
              "title": "Create Task with Add Button",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 20,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 10744,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                          "column": 24,
                          "line": 17
                        },
                        "snippet": "  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                            "column": 24,
                            "line": 17
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:57.161Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Create-Task-with-Add-Button-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Create-Task-with-Add-Button-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Create-Task-with-Add-Button-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                        "column": 24,
                        "line": 17
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "3f2e3d963f5ad528358f-943a837398a438404786",
              "file": "task-management.test.ts",
              "line": 20,
              "column": 7
            },
            {
              "title": "Create Task with Keyboard Shortcut",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 22,
                      "parallelIndex": 4,
                      "status": "failed",
                      "duration": 10753,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                          "column": 24,
                          "line": 17
                        },
                        "snippet": "  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                            "column": 24,
                            "line": 17
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:59.940Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Manag-c9641-Task-with-Keyboard-Shortcut-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Manag-c9641-Task-with-Keyboard-Shortcut-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Manag-c9641-Task-with-Keyboard-Shortcut-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                        "column": 24,
                        "line": 17
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "3f2e3d963f5ad528358f-ded9fa8812e8aa6038da",
              "file": "task-management.test.ts",
              "line": 35,
              "column": 7
            },
            {
              "title": "Edit Task Inline",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 21,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 10776,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                          "column": 24,
                          "line": 17
                        },
                        "snippet": "  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                            "column": 24,
                            "line": 17
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:09:59.940Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Edit-Task-Inline-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Edit-Task-Inline-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Edit-Task-Inline-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                        "column": 24,
                        "line": 17
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "3f2e3d963f5ad528358f-7a6512979bf063b5732f",
              "file": "task-management.test.ts",
              "line": 50,
              "column": 7
            },
            {
              "title": "Task Completion Toggle",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 23,
                      "parallelIndex": 3,
                      "status": "failed",
                      "duration": 10800,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                          "column": 24,
                          "line": 17
                        },
                        "snippet": "  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                            "column": 24,
                            "line": 17
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:10:05.048Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Task-Completion-Toggle-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Task-Completion-Toggle-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Task-Completion-Toggle-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                        "column": 24,
                        "line": 17
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "3f2e3d963f5ad528358f-34fbd9fb54b46d3f8370",
              "file": "task-management.test.ts",
              "line": 72,
              "column": 7
            },
            {
              "title": "Expanded Task View",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 24,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 10630,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                          "column": 24,
                          "line": 17
                        },
                        "snippet": "  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                            "column": 24,
                            "line": 17
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    4 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    10 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:10:07.130Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Expanded-Task-View-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Expanded-Task-View-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Expanded-Task-View-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                        "column": 24,
                        "line": 17
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "3f2e3d963f5ad528358f-9b171fd51c7725ad9e15",
              "file": "task-management.test.ts",
              "line": 98,
              "column": 7
            },
            {
              "title": "Task Context Menu Actions",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 25,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 10648,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                          "column": 24,
                          "line": 17
                        },
                        "snippet": "  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                            "column": 24,
                            "line": 17
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:10:08.477Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Task-Context-Menu-Actions-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Task-Context-Menu-Actions-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Task-Context-Menu-Actions-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                        "column": 24,
                        "line": 17
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "3f2e3d963f5ad528358f-f13140b7665e068089c9",
              "file": "task-management.test.ts",
              "line": 135,
              "column": 7
            },
            {
              "title": "Drag and Drop Reordering",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 26,
                      "parallelIndex": 4,
                      "status": "failed",
                      "duration": 10798,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                          "column": 24,
                          "line": 17
                        },
                        "snippet": "  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                            "column": 24,
                            "line": 17
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:10:11.290Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Drag-and-Drop-Reordering-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Drag-and-Drop-Reordering-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Drag-and-Drop-Reordering-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                        "column": 24,
                        "line": 17
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "3f2e3d963f5ad528358f-28e6d8d3aa9ea7d79d1e",
              "file": "task-management.test.ts",
              "line": 164,
              "column": 7
            },
            {
              "title": "Multi-Task Selection",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 27,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 10804,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                          "column": 24,
                          "line": 17
                        },
                        "snippet": "  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                            "column": 24,
                            "line": 17
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:10:11.309Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Multi-Task-Selection-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Multi-Task-Selection-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Multi-Task-Selection-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                        "column": 24,
                        "line": 17
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "3f2e3d963f5ad528358f-9a0e747e24126dc267af",
              "file": "task-management.test.ts",
              "line": 187,
              "column": 7
            },
            {
              "title": "Task Search Functionality",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 60000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 28,
                      "parallelIndex": 3,
                      "status": "failed",
                      "duration": 10798,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24",
                        "location": {
                          "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                          "column": 24,
                          "line": 17
                        },
                        "snippet": "  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                            "column": 24,
                            "line": 17
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected string: \u001b[32m\"http://localhost:3000/\u001b[7mdashboard\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"http://localhost:3000/\u001b[7mauth/login?error=Invalid%20login%20credentials\u001b[27m\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    3 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/auth/login\"\u001b[22m\n\u001b[2m    11 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"http://localhost:3000/auth/login?error=Invalid%20login%20credentials\"\u001b[22m\n\n\n  15 |     await page.getByLabel('Password').fill('TestPassword123!');\n  16 |     await page.getByRole('button', { name: 'Sign in' }).click();\n> 17 |     await expect(page).toHaveURL('/dashboard');\n     |                        ^\n  18 |   });\n  19 |\n  20 |   test('Create Task with Add Button', async ({ page }) => {\n    at /Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts:17:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-17T23:10:16.448Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Task-Search-Functionality-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Task-Search-Functionality-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results/task-management-Task-Management-Task-Search-Functionality-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/task-management.test.ts",
                        "column": 24,
                        "line": 17
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "3f2e3d963f5ad528358f-92633fda871328ab780b",
              "file": "task-management.test.ts",
              "line": 212,
              "column": 7
            }
          ]
        }
      ]
    }
  ],
  "errors": [],
  "stats": {
    "startTime": "2025-06-17T23:08:52.542Z",
    "duration": 96386.06700000001,
    "expected": 4,
    "skipped": 1,
    "unexpected": 29,
    "flaky": 0
  }
}
