{"config": {"configFile": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/playwright.config.ts", "rootDir": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-environment-setup.ts", "globalTeardown": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-environment-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.0", "workers": 5, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000, "env": {"TERM_PROGRAM": "vscode", "NODE": "/opt/homebrew/Cellar/node/23.6.1/bin/node", "INIT_CWD": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6", "SHELL": "/bin/zsh", "TERM": "xterm-256color", "TMPDIR": "/var/folders/dw/wft13hf93zsczjxh4myf5q3r0000gn/T/", "HOMEBREW_REPOSITORY": "/opt/homebrew", "npm_config_global_prefix": "/opt/homebrew", "TERM_PROGRAM_VERSION": "1.101.2", "MallocNanoZone": "0", "ORIGINAL_XDG_CURRENT_DESKTOP": "undefined", "COLOR": "1", "npm_config_noproxy": "", "npm_config_local_prefix": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6", "USER": "josh<PERSON>", "COMMAND_MODE": "unix2003", "npm_config_globalconfig": "/opt/homebrew/etc/npmrc", "SSH_AUTH_SOCK": "/private/tmp/com.apple.launchd.q4OGTtseig/Listeners", "__CF_USER_TEXT_ENCODING": "0x1F5:0x0:0x0", "npm_execpath": "/opt/homebrew/lib/node_modules/npm/bin/npm-cli.js", "PAGER": "cat", "PATH": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/node_modules/.bin:/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/node_modules/.bin:/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/node_modules/.bin:/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/node_modules/.bin:/Users/<USER>/_DEV PROJECTS/_VSCode/node_modules/.bin:/Users/<USER>/_DEV PROJECTS/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/opt/homebrew/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.rbenv/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand", "npm_package_json": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/package.json", "_": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/node_modules/.bin/playwright", "npm_config_userconfig": "/Users/<USER>/.npmrc", "npm_config_init_module": "/Users/<USER>/.npm-init.js", "__CFBundleIdentifier": "com.microsoft.VSCode", "npm_command": "exec", "PWD": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6", "npm_lifecycle_event": "npx", "EDITOR": "vi", "npm_package_name": "my-v0-project", "LANG": "en_US.UTF-8", "npm_config_npm_version": "10.9.2", "XPC_FLAGS": "0x0", "VSCODE_GIT_ASKPASS_EXTRA_ARGS": "", "npm_config_node_gyp": "/opt/homebrew/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js", "RBENV_SHELL": "zsh", "npm_package_version": "0.1.0", "XPC_SERVICE_NAME": "0", "HOME": "/Users/<USER>", "SHLVL": "2", "VSCODE_GIT_ASKPASS_MAIN": "/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js", "HOMEBREW_PREFIX": "/opt/homebrew", "npm_config_cache": "/Users/<USER>/.npm", "LOGNAME": "josh<PERSON>", "LESS": "-FX", "npm_lifecycle_script": "playwright", "VSCODE_GIT_IPC_HANDLE": "/var/folders/dw/wft13hf93zsczjxh4myf5q3r0000gn/T/vscode-git-fb6c6ae1c2.sock", "npm_config_user_agent": "npm/10.9.2 node/v23.6.1 darwin arm64 workspaces/false", "HOMEBREW_CELLAR": "/opt/homebrew/Cellar", "INFOPATH": "/opt/homebrew/share/info:", "GIT_ASKPASS": "/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh", "VSCODE_GIT_ASKPASS_NODE": "/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)", "GIT_PAGER": "cat", "npm_node_execpath": "/opt/homebrew/Cellar/node/23.6.1/bin/node", "npm_config_prefix": "/opt/homebrew", "COLORTERM": "truecolor", "NEXT_PUBLIC_SUPABASE_URL": "https://rxdwcvzxgzwuapdxxbnt.supabase.co", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ4ZHdjdnp4Z3p3dWFwZHh4Ym50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwODcxMzcsImV4cCI6MjA2NTY2MzEzN30.93S6YrCt-5h-u04KrkekOANPBjKkB95rjc_OsiXWWNk", "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ4ZHdjdnp4Z3p3dWFwZHh4Ym50Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA4NzEzNywiZXhwIjoyMDY1NjYzMTM3fQ.BE2D-f0hz7cnlaFdyjVW46euXvAYbM1mRyWs8NMjHLg", "NEXT_PUBLIC_SITE_URL": "http://localhost:3000", "NODE_ENV": "test", "PLAYWRIGHT_HEADLESS": "false", "OPENAI_API_KEY": "********************************************************************************************************************************************************************", "TEST_ENVIRONMENT": "true", "TEST_PROJECT_NAME": "listless-testing", "PLAYWRIGHT_BASE_URL": "http://localhost:3000", "PLAYWRIGHT_TIMEOUT": "30000", "PLAYWRIGHT_RETRIES": "2", "TEST_USER_PREFIX": "tm2-playwright-test", "TEST_USER_DOMAIN": "listless-testing.local", "TEST_USER_RETENTION_HOURS": "24"}}}, "suites": [], "errors": [{"message": "Error: \n❌ Failed to connect to test database: TypeError: fetch failed\n\nPlease verify:\n1. Test Supabase instance is running\n2. Environment variables are correct\n3. Network connectivity is available\n    ", "stack": "Error: \n❌ Failed to connect to test database: TypeError: fetch failed\n\nPlease verify:\n1. Test Supabase instance is running\n2. Environment variables are correct\n3. Network connectivity is available\n    \n    at testDatabaseConnectivity (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-environment-setup.ts:61:11)\n    at globalSetup (/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-environment-setup.ts:30:3)", "location": {"file": "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6/tests/test-environment-setup.ts", "column": 11, "line": 61}, "snippet": "\u001b[90m   at \u001b[39mtest-environment-setup.ts:61\n\n\u001b[0m \u001b[90m 59 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Database connectivity verified'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 60 |\u001b[39m   } \u001b[36mcatch\u001b[39m (error) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 61 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`\u001b[39m\n \u001b[90m    |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 62 |\u001b[39m \u001b[32m❌ Failed to connect to test database: ${error}\u001b[39m\n \u001b[90m 63 |\u001b[39m \u001b[32m\u001b[39m\n \u001b[90m 64 |\u001b[39m \u001b[32mPlease verify:\u001b[39m\u001b[0m"}], "stats": {"startTime": "2025-06-28T10:43:38.856Z", "duration": 5569.5289999999995, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}