{"name": "my-v0-project", "version": "0.1.0", "private": true, "repository": {"type": "git", "url": "https://github.com/josh000111/Listless_V0_8-6.git"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "test:auth": "playwright test tests/auth.test.ts", "test:tasks": "playwright test tests/task-management.test.ts", "test:projects": "playwright test tests/project-area-management.test.ts", "test:verify-env": "node -e \"console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)\""}, "dependencies": {"@dnd-kit/core": "latest", "@dnd-kit/sortable": "latest", "@dnd-kit/utilities": "latest", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.81.0", "@tanstack/react-query-devtools": "^5.81.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "openai": "^4.73.1", "react": "^19", "react-day-picker": "^9.8.0", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@playwright/test": "^1.53.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "dotenv": "^16.5.0", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}