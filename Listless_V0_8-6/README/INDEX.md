# Developer Onboarding Documentation

Welcome to the Listless project! This folder contains essential documentation for new developers joining the project.

## 📋 Quick Start Guide

Read these documents in order for the fastest onboarding:

### 1. **README.md** - Project Overview
Start here to understand what Listless is, its features, and technology stack.

### 2. **Augment_Rules.txt** - Development Standards
Essential coding standards and AI agent rules for consistent development practices.

### 3. **API_DOCUMENTATION.md** - API Reference
Complete API endpoint documentation for backend integration.

### 4. **RLS_SECURITY_GUIDE.md** - Security Implementation
Row Level Security implementation guide - critical for data protection.

### 5. **TESTING_GUIDE.md** - Testing Standards
Application-specific testing guide and best practices.

## 🎯 What You'll Find Here

- **Project Information**: Complete overview of Listless features and capabilities
- **Development Standards**: Coding rules, architecture patterns, and best practices
- **API Reference**: Endpoint documentation for backend integration
- **Security Guidelines**: RLS policies and security implementation details
- **Testing Framework**: Testing standards and procedures

## 🔗 Additional Resources

For comprehensive project documentation, also review:
- **Repository Root README**: `/README.md` - Development environment overview
- **Full Documentation**: `/docs/` - Complete project documentation
- **Testing Infrastructure**: `/testing/` - Comprehensive testing setup guides

## 🚀 Getting Started

1. Read the main README.md to understand the project
2. Review Augment_Rules.txt for development standards
3. Set up your development environment following the guides
4. Review security and API documentation
5. Run the test suite to verify your setup

Happy coding! 🎉
